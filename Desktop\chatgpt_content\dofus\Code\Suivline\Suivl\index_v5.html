<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dofus Character Stats Simulator - Professional Dashboard</title>
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --background-color: #f8f9fa;
            --background-color2: #ffffff;
            --card-background: #ffffff;
            --text-color: #2c3e50;
            --border-color: #dee2e6;
            --tier-color: #5c4e00;
            --option-text-color: #495057;
            --main-text-color: #6c757d;
            --highlight-color: #f8fbff;
            --highlight-border: #575a5e80;
            --highlight-shadow: rgba(190, 203, 214, 0.37);
            --shadow: 0 4px 6px rgba(0,0,0,0.07);
            --shadow-hover: 0 8px 25px rgba(0,0,0,0.15);
        }

        [data-theme="dark"] {
            --primary-color: #34495e;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --background-color: #182936;
            --background-color2: #182936;
            --card-background: #2d3748;
            --text-color: #e2e8f0;
            --border-color: #4a5568;
            --tier-color: #ffee00;
            --option-text-color: #b8c5d1;
            --main-text-color: #8a9ba8;
            --highlight-color: #182b3a;
            --highlight-border: #899199;
            --highlight-shadow: rgba(94, 94, 94, 0.4);
            --shadow: 0 4px 6px rgba(0,0,0,0.3);
            --shadow-hover: 0 8px 25px rgba(0,0,0,0.4);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
            transition: all 0.3s ease;
        }

        .dashboard-container {
            display: grid;
            grid-template-columns: 280px 1fr 280px;
            gap: 12px;
            max-width: 1600px;
            margin: 0 auto;
            padding: 12px;
            min-height: 100vh;
        }

        /* Left Sidebar - Controls & Stats Counter */
        .left-sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
            position: sticky;
            top: 20px;
            height: fit-content;
        }

        .header-card {
            background: var(--card-background);
            padding: 25px;
            border-radius: 12px;
            box-shadow: var(--shadow);
            text-align: center;
        }

        .header-card h1 {
            color: var(--main-text-color);
            margin-bottom: 8px;
            font-size: 1.8em;
            font-weight: 700;
        }

        .header-card p {
            color: var(--text-color);
            opacity: 0.7;
            font-size: 0.9em;
        }

        .controls-card {
            background: var(--card-background);
            padding: 20px;
            border-radius: 12px;
            box-shadow: var(--shadow);
        }

        .controls-title {
            font-size: 1.1em;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--main-text-color);
        }

        .controls-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .btn {
            background: var(--secondary-color);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .btn:hover {
            background: #2980b9;
            transform: translateY(-1px);
            box-shadow: var(--shadow-hover);
        }

        .btn-danger {
            background: var(--accent-color);
            grid-column: 1 / -1;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .search-card {
            background: var(--card-background);
            padding: 20px;
            border-radius: 12px;
            box-shadow: var(--shadow);
        }

        .search-title {
            font-size: 1.1em;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--main-text-color);
        }

        .search-box {
            position: relative;
        }

        #statSearch {
            width: 100%;
            padding: 12px 40px 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 14px;
            background: var(--card-background);
            color: var(--main-text-color);
            transition: border-color 0.3s ease;
        }

        #statSearch:focus {
            outline: none;
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        #statSearch::placeholder {
            color: var(--main-text-color);
            opacity: 0.7;
        }

        .search-clear {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            font-size: 16px;
            cursor: pointer;
            color: var(--text-color);
            opacity: 0.6;
            transition: opacity 0.3s ease;
        }

        .search-clear:hover {
            opacity: 1;
        }

        .tier-select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 14px;
            background: var(--card-background);
            color: var(--text-color);
            transition: border-color 0.3s ease;
            cursor: pointer;
        }

        .tier-select:focus {
            outline: none;
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .tier-container {
            margin-bottom: 25px;
            background: var(--background-color);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid var(--border-color);
        }

        .tier-container h3 {
            color: var(--option-text-color);
            margin-bottom: 15px;
            font-size: 1.1em;
            font-weight: 600;
            padding-bottom: 8px;
            border-bottom: 2px solid #a1a1a733;
        }

        .stat-group {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 8px;
        }



        /* Center Content - Tiers */
        .center-content {
            display: flex;
            flex-direction: column;
            gap: 12px;
            overflow-y: auto;
            max-height: calc(100vh - 30px);
            min-height: 0; /* Allow flex shrinking */
            padding-top: 10px;
        }

        .tier-section {
            background: var(--card-background);
            border-radius: 12px;
            box-shadow: var(--shadow);
            overflow: visible; /* Changed from hidden to visible */
            flex-shrink: 0; /* Prevent shrinking */
            transition: all 0.3s ease;
        }

        .tier-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            user-select: none;
        }

        .tier-header:hover {
            background: linear-gradient(135deg, #34495e, #2980b9);
            transform: translateY(-1px);
        }

        .tier-header.active {
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
        }

        .tier-title {
            font-size: 1.2em;
            font-weight: 600;
        }

        .tier-limit {
            background: rgba(255, 255, 255, 0.2);
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 0.85em;
        }

        .tier-toggle {
            font-size: 1.2em;
            transition: transform 0.3s ease;
        }

        .tier-toggle.rotated {
            transform: rotate(180deg);
        }

        .tier-content {
            padding: 12px;
            display: none;
            animation: slideDown 0.3s ease;
            max-height: none; /* Remove height restrictions */
            overflow: visible; /* Allow content to be fully visible */
        }

        .tier-content.show {
            display: block;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
            gap: 8px;
        }

        .stat-button {
            background: var(--card-background);
            border: 2px solid var(--border-color);
            padding: 8px 10px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            color: var(--text-color);
            text-align: left;
        }

        .stat-button:hover {
            border-color: var(--secondary-color);
            background: rgba(52, 152, 219, 0.05);
            transform: translateY(-1px);
            box-shadow: var(--shadow-hover);
        }

        .stat-button.selected {
            background: var(--secondary-color);
            color: white;
            border-color: var(--secondary-color);
            box-shadow: var(--shadow-hover);
        }

        .stat-button.selected:hover {
            background: #2980b9;
        }

        .stat-icon {
            width: 20px;
            height: 20px;
            object-fit: contain;
            flex-shrink: 0;
        }

        /* Right Sidebar - Selected Stats */
        .right-sidebar {
            display: flex;
            flex-direction: column;
            gap: 12px;
            position: sticky;
            top: 20px;
            height: fit-content;
            min-width: 280px; /* Ensure minimum width */
            max-width: 320px; /* Prevent it from getting too wide */
        }

        .selected-stats {
            background: var(--card-background);
            padding: 16px;
            border-radius: 8px;
            box-shadow: var(--shadow);
            max-height: calc(100vh - 200px);
            overflow-y: auto;
            /* border: 2px solid #444; */
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .selected-stats h3 {
            color: var(--tier-color);
            margin-bottom: 12px;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            border-bottom: 1px solid #8888884d;
            padding-bottom: 8px;
        }

        .selected-list {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .selected-item {
            background: transparent;
            padding: 4px 8px;
            border-radius: 0;
            display: flex;
            align-items: center;
            gap: 8px;
            border: none;
            transition: all 0.2s ease;
            font-size: 13px;
            line-height: 1.3;
        }

        .selected-item:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .selected-info {
            display: flex;
            align-items: center;
            gap: 6px;
            flex: 1;
        }

        .selected-item .stat-icon {
            width: 16px;
            height: 16px;
            flex-shrink: 0;
        }

        .selected-item .stat-text {
            color: #ffffff;
            flex: 1;
            font-weight: 500;
        }

        .selected-tier {
            font-size: 10px;
            background: #8b4513;
            color: #f4d03f;
            padding: 1px 4px;
            border-radius: 2px;
            margin-left: 4px;
            font-weight: bold;
        }

        .remove-btn {
            background: #8b4513;
            color: white;
            border: none;
            width: 16px;
            height: 16px;
            border-radius: 2px;
            cursor: pointer;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            opacity: 0.7;
        }

        .remove-btn:hover {
            background: #a0522d;
            opacity: 1;
        }

        .tier-stats-group {
            margin-bottom: 12px;
            border: none;
            border-radius: 0;
            overflow: visible;
        }

        .tier-stats-header {
            background: transparent;
            color: var(--tier-color);
            padding: 6px 0;
            font-weight: bold;
            font-size: 12px;
            text-transform: uppercase;
            border-bottom: 1px solid #8888884d;
            margin-bottom: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .tier-stats-header:hover {
            color: var(--secondary-color);
            background: rgba(255, 255, 255, 0.05);
            padding-left: 4px;
        }

        .tier-stats-content {
            padding: 0;
        }

        .tier-stats-content .selected-item {
            margin-bottom: 8px;
        }

        .tier-stats-content .selected-item:last-child {
            margin-bottom: 0;
        }

        .placeholder-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 4px 8px;
            background: transparent;
            border: 1px dashed #555;
            border-radius: 0;
            margin-bottom: 2px;
            opacity: 0.5;
            transition: all 0.2s ease;
            font-size: 13px;
            cursor: pointer;
        }

        .placeholder-item:hover {
            opacity: 0.8;
            border-color: var(--secondary-color);
            background: rgba(255, 255, 255, 0.05);
        }

        .placeholder-info {
            display: flex;
            align-items: center;
            gap: 6px;
            flex: 1;
        }

        .placeholder-icon {
            font-size: 12px;
            opacity: 0.6;
        }

        .placeholder-text {
            color: var(--main-text-color);
            font-style: italic;
            font-size: 12px;
        }

        /* Option highlighting styles */
        .option-group.highlighted {
            background: var(--highlight-color);
            border: 2px solid var(--highlight-border);
            box-shadow: 0 0 25px var(--highlight-shadow), 0 0 50px var(--highlight-shadow);
            transform: scale(1.001);
            z-index: 10;
            position: relative;
        }

        .option-group {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .tier-content.highlighting .option-group:not(.highlighted) {
            opacity: 0.3;
            filter: blur(2px);
            transform: scale(0.96);
        }

        /* Add a subtle pulse animation to the highlighted option */
        .option-group.highlighted {
            animation: highlightPulse 2s ease-in-out infinite;
        }

        /* @keyframes highlightPulse {
            0%, 100% {
                box-shadow: 0 0 25px rgba(255, 238, 0, 0.4), 0 0 50px rgba(255, 238, 0, 0.2);
            }
            50% {
                box-shadow: 0 0 30px rgba(255, 238, 0, 0.6), 0 0 60px rgba(255, 238, 0, 0.3);
            }
        } */

        .highlight {
            background-color: #8b7324;
            color: #041185;
            padding: 2px 4px;
            border-radius: 3px;
        }

        [data-theme="dark"] .highlight {
            background-color: #f39c12;
            color: white;
        }

        /* Comparison Modal Styles */
        #compareModal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
        }

        .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: var(--card-background);
            border-radius: 12px;
            max-width: 90vw;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            border: 1px solid var(--border-color);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .modal-header h2 {
            margin: 0;
            color: var(--text-color);
            font-size: 1.5em;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: var(--text-color);
            padding: 5px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .modal-close:hover {
            background: var(--border-color);
        }

        .modal-body {
            padding: 20px;
            min-width: 600px;
        }

        .build-selector h3 {
            color: var(--option-text-color);
            margin-bottom: 15px;
        }

        .build-list {
            display: grid;
            gap: 10px;
            margin-bottom: 20px;
            max-height: 300px;
            overflow-y: auto;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
        }

        .build-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            background: var(--background-color2);
        }

        .build-item:hover {
            background: var(--highlight-color);
            border-color: var(--highlight-border);
        }

        .build-item input[type="checkbox"] {
            margin: 0;
        }

        .build-info {
            flex: 1;
        }

        .build-name {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 4px;
        }

        .build-date {
            font-size: 0.9em;
            color: var(--search-text-color);
        }

        .compare-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .comparison-results {
            margin-top: 20px;
        }

        .comparison-results h3 {
            color: var(--option-text-color);
            margin-bottom: 15px;
        }

        .comparison-table {
            overflow-x: auto;
        }

        .comparison-table table {
            width: 100%;
            border-collapse: collapse;
            background: var(--background-color2);
            border-radius: 8px;
            overflow: hidden;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .comparison-table th {
            background: var(--background-color);
            font-weight: 600;
            color: var(--text-color);
        }

        .stat-name {
            font-weight: 500;
            color: var(--option-text-color);
        }

        .stat-value {
            text-align: center;
            font-weight: 600;
        }

        .has-stat {
            color: var(--success-color);
        }

        .no-stat {
            color: var(--search-text-color);
            opacity: 0.6;
        }

        /* Save Modal Styles */
        .save-modal {
            min-width: 500px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--option-text-color);
        }

        .form-group input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 14px;
            background: var(--background-color2);
            color: var(--text-color);
            transition: border-color 0.3s ease;
        }

        .form-group input[type="text"]:focus {
            outline: none;
            border-color: var(--secondary-color);
        }

        .tier-selection {
            display: grid;
            gap: 8px;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--background-color2);
        }

        .tier-option {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .tier-option:hover:not(.disabled) {
            background: var(--highlight-color);
        }

        .tier-option.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .tier-option input[type="radio"] {
            margin: 0;
        }

        .save-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        /* Notification Styles */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 2000;
            padding: 16px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 400px;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: var(--success-color);
            color: white;
        }

        .notification.error {
            background: var(--accent-color);
            color: white;
        }

        .notification.info {
            background: var(--secondary-color);
            color: white;
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .notification-icon {
            font-weight: bold;
            font-size: 16px;
        }

        /* Suivlines Page Styles */
        .suivlines-page {
            min-width: 800px;
            max-width: 1200px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--search-text-color);
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: var(--option-text-color);
        }

        .suivlines-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            padding: 10px;
        }

        /* Dofus-style Suivline Cards */
        .suivline-card {
            background: linear-gradient(145deg, #2a2a2a 0%, #1e1e1e 100%);
            border: 2px solid #4a4a4a;
            border-radius: 12px;
            padding: 16px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        [data-theme="light"] .suivline-card {
            background: linear-gradient(145deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #dee2e6;
        }

        .suivline-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            border-color: #ffee00;
        }

        .suivline-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #ffee00, #ffd700, #ffee00);
        }

        .suivline-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid rgba(255, 238, 0, 0.2);
        }

        .suivline-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(145deg, #ffee00, #ffd700);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            box-shadow: 0 2px 8px rgba(255, 238, 0, 0.3);
        }

        .suivline-info {
            flex: 1;
        }

        .suivline-name {
            font-size: 16px;
            font-weight: 700;
            color: #ffee00;
            margin-bottom: 4px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }

        [data-theme="light"] .suivline-name {
            color: #5c4e00;
            text-shadow: none;
        }

        .suivline-meta {
            display: flex;
            gap: 12px;
            font-size: 12px;
            color: #b8b8b8;
        }

        [data-theme="light"] .suivline-meta {
            color: #6c757d;
        }

        .suivline-date::before {
            content: '📅 ';
        }

        .suivline-stats::before {
            content: '⚔️ ';
        }

        .suivline-preview {
            margin-bottom: 16px;
            min-height: 80px;
        }

        .tier-preview {
            margin-bottom: 8px;
            padding: 8px;
            background: rgba(255, 238, 0, 0.1);
            border-radius: 6px;
            border-left: 3px solid #ffee00;
        }

        .tier-preview-name {
            font-size: 12px;
            font-weight: 600;
            color: #ffee00;
            margin-bottom: 4px;
            text-transform: uppercase;
        }

        [data-theme="light"] .tier-preview-name {
            color: #5c4e00;
        }

        .tier-preview-stats {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
        }

        .stat-preview {
            background: rgba(255, 255, 255, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            color: #e0e0e0;
            border: 1px solid rgba(255, 238, 0, 0.3);
        }

        [data-theme="light"] .stat-preview {
            background: rgba(0, 0, 0, 0.05);
            color: #495057;
            border: 1px solid rgba(92, 78, 0, 0.3);
        }

        .stat-more {
            background: rgba(255, 238, 0, 0.2);
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            color: #ffee00;
            font-weight: 600;
        }

        [data-theme="light"] .stat-more {
            background: rgba(92, 78, 0, 0.2);
            color: #5c4e00;
        }

        .no-stats {
            color: #888;
            font-style: italic;
            text-align: center;
            padding: 20px;
        }

        .suivline-actions {
            display: flex;
            gap: 8px;
            justify-content: space-between;
        }

        .suivline-actions .btn {
            flex: 1;
            padding: 8px 12px;
            font-size: 12px;
            border-radius: 6px;
        }

        /* Confirm Modal */
        .confirm-modal {
            min-width: 400px;
        }

        .warning-text {
            color: var(--accent-color);
            font-style: italic;
            margin-top: 10px;
        }

        .confirm-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        /* Comparison Modal Styles */
        .comparison-modal {
            min-width: 900px;
            max-width: 95vw;
        }

        .build-stats {
            font-size: 0.85em;
            color: var(--search-text-color);
            margin-top: 2px;
        }

        .suivlines-comparison {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .suivline-comparison-card {
            background: linear-gradient(145deg, #2a2a2a 0%, #1e1e1e 100%);
            border: 2px solid #4a4a4a;
            border-radius: 12px;
            padding: 16px;
            position: relative;
            overflow: hidden;
        }

        [data-theme="light"] .suivline-comparison-card {
            background: linear-gradient(145deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #dee2e6;
        }

        .suivline-comparison-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #ffee00, #ffd700, #ffee00);
        }

        .comparison-card-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid rgba(255, 238, 0, 0.2);
        }

        .comparison-card-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(145deg, #ffee00, #ffd700);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            box-shadow: 0 2px 6px rgba(255, 238, 0, 0.3);
        }

        .comparison-card-info {
            flex: 1;
        }

        .comparison-card-name {
            font-size: 14px;
            font-weight: 700;
            color: #ffee00;
            margin-bottom: 4px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }

        [data-theme="light"] .comparison-card-name {
            color: #5c4e00;
            text-shadow: none;
        }

        .comparison-card-meta {
            display: flex;
            gap: 10px;
            font-size: 11px;
            color: #b8b8b8;
        }

        [data-theme="light"] .comparison-card-meta {
            color: #6c757d;
        }

        .tier-characteristics {
            margin-bottom: 12px;
            padding: 8px;
            background: rgba(255, 238, 0, 0.1);
            border-radius: 6px;
            border-left: 3px solid #ffee00;
        }

        .tier-char-header {
            font-size: 11px;
            font-weight: 600;
            color: #ffee00;
            margin-bottom: 6px;
            text-transform: uppercase;
        }

        [data-theme="light"] .tier-char-header {
            color: #5c4e00;
        }

        .tier-char-stats {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .char-stat-item {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 3px 6px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
            border: 1px solid rgba(255, 238, 0, 0.2);
        }

        [data-theme="light"] .char-stat-item {
            background: rgba(0, 0, 0, 0.03);
            border: 1px solid rgba(92, 78, 0, 0.2);
        }

        .char-stat-icon {
            width: 16px;
            height: 16px;
            flex-shrink: 0;
        }

        .char-stat-name {
            font-size: 11px;
            color: #e0e0e0;
            font-weight: 500;
        }

        [data-theme="light"] .char-stat-name {
            color: #495057;
        }

        .no-characteristics {
            color: #888;
            font-style: italic;
            text-align: center;
            padding: 20px;
            font-size: 12px;
        }

        /* Enhanced Comparison Modal Styles */
        .comparison-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: var(--background-color2);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .search-section {
            flex: 1;
            max-width: 300px;
        }

        #suivlineSearch {
            width: 100%;
            padding: 10px 12px;
            border: 2px solid var(--border-color);
            border-radius: 6px;
            background: var(--background-color);
            color: var(--text-color);
            font-size: 14px;
        }

        #suivlineSearch:focus {
            outline: none;
            border-color: var(--secondary-color);
        }

        .view-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .results-count {
            color: var(--search-text-color);
            font-size: 14px;
        }

        /* Build List Styles */
        .build-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 10px;
            background: var(--background-color2);
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .build-item {
            display: flex;
            align-items: flex-start;
            gap: 8px;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--background-color);
            transition: all 0.2s;
            margin-bottom: 0;
        }

        .build-item:hover {
            background: var(--highlight-color);
            border-color: var(--highlight-border);
        }

        .build-item.clickable {
            cursor: pointer;
        }

        .build-item.clickable:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        [data-theme="light"] .build-item.clickable:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .build-item.detailed {
            flex-direction: column;
            align-items: stretch;
            grid-column: 1 / -1; /* Span full width */
        }

        .build-checkbox {
            margin-top: 2px;
        }

        .build-preview {
            flex: 1;
        }

        .build-header {
            margin-bottom: 8px;
        }

        .build-name {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 3px;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 13px;
        }

        .recent-badge {
            background: var(--success-color);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 500;
        }

        .build-meta {
            display: flex;
            gap: 8px;
            font-size: 11px;
            color: var(--search-text-color);
        }

        .build-quick-preview {
            margin-top: 8px;
        }

        .quick-preview-items {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }

        .compact-preview-items {
            font-size: 11px;
            color: var(--option-text-color);
            line-height: 1.3;
        }

        .preview-item {
            background: rgba(255, 238, 0, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            color: var(--option-text-color);
            border: 1px solid rgba(255, 238, 0, 0.3);
        }

        .preview-more {
            background: rgba(255, 238, 0, 0.2);
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            color: #ffee00;
            font-weight: 600;
        }

        [data-theme="light"] .preview-more {
            color: #5c4e00;
        }

        .no-preview {
            color: var(--search-text-color);
            font-style: italic;
            font-size: 11px;
        }

        .build-actions {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        /* Detailed View Styles */
        .build-header-detailed {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }

        .build-info-detailed {
            flex: 1;
        }

        .tier-selection-grid {
            display: grid;
            gap: 8px;
        }

        .tier-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 4px;
        }

        .tier-grid-header {
            font-size: 11px;
            font-weight: 600;
            color: var(--option-text-color);
            margin-bottom: 6px;
            padding-bottom: 4px;
            border-bottom: 1px solid var(--border-color);
        }

        .tier-selection-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 8px;
            background: var(--background-color2);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            transition: all 0.2s;
            font-size: 11px;
        }

        .tier-selection-item:hover:not(.disabled) {
            background: var(--highlight-color);
            border-color: var(--highlight-border);
        }

        .tier-selection-item.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .tier-info {
            flex: 1;
        }

        .tier-name {
            font-weight: 600;
            color: var(--text-color);
            font-size: 11px;
            margin-bottom: 2px;
        }

        .tier-stats-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            font-size: 10px;
        }

        .tier-stat-item {
            background: rgba(255, 238, 0, 0.1);
            padding: 1px 4px;
            border-radius: 3px;
            font-size: 10px;
            color: var(--option-text-color);
            border: 1px solid rgba(255, 238, 0, 0.3);
        }

        .tier-stat-more {
            background: rgba(255, 238, 0, 0.2);
            padding: 1px 4px;
            border-radius: 3px;
            font-size: 10px;
            color: #ffee00;
            font-weight: 600;
        }

        [data-theme="light"] .tier-stat-more {
            color: #5c4e00;
        }

        .no-tier-stats {
            color: var(--search-text-color);
            font-style: italic;
            font-size: 10px;
        }

        /* Pagination Styles */
        .pagination-controls {
            margin: 15px 0;
            text-align: center;
        }

        .pagination {
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .pagination .btn {
            min-width: 35px;
            height: 35px;
            padding: 6px 10px;
            font-size: 12px;
        }

        .pagination .btn.active {
            background: var(--secondary-color);
            color: white;
            border-color: var(--secondary-color);
        }

        .pagination .btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }

        .btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }

        .pagination-dots {
            color: var(--search-text-color);
            padding: 0 5px;
        }

        /* Compare Actions */
        .compare-actions {
            text-align: center;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid var(--border-color);
        }

        .selection-info {
            margin-top: 8px;
            font-size: 12px;
            color: var(--search-text-color);
        }

        /* Advanced Comparison Results */
        .comparison-header {
            margin-bottom: 20px;
            text-align: center;
        }

        .comparison-summary {
            color: var(--search-text-color);
            font-size: 14px;
            margin-top: 5px;
        }

        .advanced-comparison {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .advanced-comparison-card {
            background: linear-gradient(145deg, #2a2a2a 0%, #1e1e1e 100%);
            border: 2px solid #4a4a4a;
            border-radius: 12px;
            padding: 16px;
            position: relative;
            overflow: hidden;
        }

        [data-theme="light"] .advanced-comparison-card {
            background: linear-gradient(145deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #dee2e6;
        }

        .advanced-comparison-card.tier {
            border-color: #ff6b35;
        }

        .advanced-comparison-card.tier::before {
            background: linear-gradient(90deg, #ff6b35, #ff8c42, #ff6b35);
        }

        .advanced-comparison-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #ffee00, #ffd700, #ffee00);
        }

        .comparison-card-type {
            font-size: 11px;
            color: var(--search-text-color);
            font-style: italic;
            margin-bottom: 4px;
        }

        .advanced-comparison-card.tier .comparison-card-type {
            color: #ff6b35;
            font-weight: 600;
        }

        [data-theme="light"] .advanced-comparison-card.tier .comparison-card-type {
            color: #d63384;
        }

        /* Show Builds Section */
        .show-builds-section {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: var(--background-color2);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .hidden {
            display: none !important;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .dashboard-container {
                grid-template-columns: 280px 1fr 280px;
            }
        }

        @media (max-width: 1024px) {
            .dashboard-container {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto 1fr;
            }

            .left-sidebar {
                flex-direction: row;
                gap: 15px;
                position: static;
            }

            .left-sidebar > * {
                flex: 1;
            }

            .right-sidebar {
                position: static;
            }

            .controls-grid {
                grid-template-columns: repeat(4, 1fr);
            }

            .counter-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        @media (max-width: 768px) {
            .dashboard-container {
                padding: 15px;
                gap: 15px;
            }

            .left-sidebar {
                flex-direction: column;
            }

            .controls-grid {
                grid-template-columns: 1fr 1fr;
            }

            .counter-grid {
                grid-template-columns: 1fr 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Left Sidebar -->
        <div class="left-sidebar">
            <!-- Header -->
            <div class="header-card">
                <h1>🎮 Dofus Stats</h1>
                <p>Suivline Simulator</p>
            </div>

            <!-- Controls -->
            <div class="controls-card">
                <div class="controls-title">⚙️ Paramètres</div>
                <div class="controls-grid">
                    <button class="btn" id="darkModeToggle">🌙 Sombre</button>
                    <button class="btn" id="saveBuildBtn">💾 Sauvegarder</button>
                    <button class="btn" id="loadBuildBtn">� Charger</button>
                    <button class="btn" id="compareBtn">⚖️ Comparer</button>
                    <button class="btn btn-danger" id="resetButton">🗑️ Réinitialiser tout</button>
                </div>
            </div>

            <!-- Search -->
            <div class="search-card">
                <div class="search-title">🔍 Rechercher</div>
                <div class="search-box">
                    <input type="text" id="statSearch" placeholder="Force, Critique, PA,..." />
                    <button class="search-clear" id="clearSearch">✕</button>
                </div>
            </div>


        </div>

        <!-- Center Content - Accordion Tiers -->
        <div class="center-content" id="tiersAccordion">
            <!-- All tiers will be generated here as accordion -->
        </div>

        <!-- Right Sidebar - Selected Stats -->
        <div class="right-sidebar">
            <div class="selected-stats">
                <h3>✨ Charactéristiques</h3>
                <div class="selected-list" id="selectedStatsList">
                    <div style="text-align: center; opacity: 0.6; padding: 20px;">
                        No stats selected yet.<br>
                        <small>Cliquez sur les statistiques des niveaux pour les ajouter !</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for tier limit exceeded -->
    <div id="tierModal" class="modal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); backdrop-filter: blur(5px);">
        <div class="modal-content" style="background-color: var(--card-background); margin: 10% auto; padding: 30px; border-radius: 15px; width: 90%; max-width: 380px; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
            <h3 style="color: var(--main-text-color); margin-bottom: 20px; font-size: 1.4em;">Limite de Tier dépassée</h3>
            <p>Choisissez une statistique à remplacer :</p>
            <div id="replaceOptions"></div>
            <div style="display: flex; gap: 15px; justify-content: flex-end; margin-top: 25px;">
                <button class="btn btn-danger" onclick="closeTierModal()">Cancel</button>
            </div>
        </div>
    </div>

    <script>
        // Icon mapping for Dofus stats using DofusDB.fr
        const statIcons = {
          // AP/MP related
          "PA": "https://dofusdb.fr/icons/effects/pa.png",
          "PM": "https://dofusdb.fr/icons/effects/pm.png",
          "PO": "https://dofusdb.fr/icons/effects/po.png",

          // Secondary stats
          "Initiative": "https://dofusdb.fr/icons/effects/initiative.png",
          "Prospection": "https://dofusdb.fr/icons/effects/pp.png",
          "Critique": "https://dofusdb.fr/icons/effects/critique.png",
          "Sagesse": "https://dofusdb.fr/icons/effects/sagesse.png",
          "Puissance": "https://dofusdb.fr/icons/effects/puissance.png",
          "Soins": "https://dofusdb.fr/icons/effects/soin.png",
          "Soin": "https://dofusdb.fr/icons/effects/soin.png",

          // Movement stats
          "Tacle": "https://dofusdb.fr/icons/effects/tacle.png",
          "Fuite": "https://dofusdb.fr/icons/effects/fuite.png",

          // AP/MP manipulation (fixed names)
          "Esquive PA": "https://dofusdb.fr/icons/effects/esquivePA.png",
          "Esquive PM": "https://dofusdb.fr/icons/effects/esquivePM.png",
          "Retrait PA": "https://dofusdb.fr/icons/effects/retraitPA.png",
          "Retrait PM": "https://dofusdb.fr/icons/effects/retraitPM.png",

          // Primary elemental stats
          "Force": "https://dofusdb.fr/icons/effects/terre.png",
          "Intelligence": "https://dofusdb.fr/icons/effects/feu.png",
          "Chance": "https://dofusdb.fr/icons/effects/eau.png",
          "Agilité": "https://dofusdb.fr/icons/effects/air.png",
          "Vitalité": "https://dofusdb.fr/icons/effects/pv.png",
          "Vita": "https://dofusdb.fr/icons/effects/pv.png",

          // Resistances
          "Résistance Neutre": "https://dofusdb.fr/icons/effects/resNeutre.png",
          "Résistance Terre": "https://dofusdb.fr/icons/effects/resTerre.png",
          "Résistance Feu": "https://dofusdb.fr/icons/effects/resFeu.png",
          "Résistance Eau": "https://dofusdb.fr/icons/effects/resEau.png",
          "Résistance Air": "https://dofusdb.fr/icons/effects/resAir.png",
          "Résistance Critique": "https://dofusdb.fr/icons/effects/resCrit.png",
          "Résistance Poussée": "https://dofusdb.fr/icons/effects/resPoussee.png",

          // Elemental damages (using same icons as elements)
          "Dommages Neutre": "https://dofusdb.fr/icons/effects/neutre.png",
          "Dommages Terre": "https://dofusdb.fr/icons/effects/terre.png",
          "Dommages Feu": "https://dofusdb.fr/icons/effects/feu.png",
          "Dommages Eau": "https://dofusdb.fr/icons/effects/eau.png",
          "Dommages Air": "https://dofusdb.fr/icons/effects/air.png",

          // Special damages
          "Dommages": "https://dofusdb.fr/icons/effects/dommages.png",
          "Dommages Critique": "https://dofusdb.fr/icons/effects/dmgCritique.png",
          "Dommages Poussée": "https://dofusdb.fr/icons/effects/dmgPoussee.png",

          // Weapon/Spell damages
          "Dommages aux Sorts": "https://dofusdb.fr/icons/effects/dmgSort.png",
          "Dommages d'Armes": "https://dofusdb.fr/icons/effects/dmgArme.png",
          "Dommages Distance": "https://dofusdb.fr/icons/effects/dmgDistance.png",
          "Dommages Mêlée": "https://dofusdb.fr/icons/effects/dmgMelee.png",

          // Weapon/Spell resistances
          "Résistance aux Sorts": "https://dofusdb.fr/icons/effects/resSort.png",
          "Résistance d'Armes": "https://dofusdb.fr/icons/effects/resArme.png",
          "Résistance Distance": "https://dofusdb.fr/icons/effects/resDistance.png",
          "Résistance Mêlée": "https://dofusdb.fr/icons/effects/resMelee.png",

          // Special stats
          "Invocation": "https://dofusdb.fr/icons/effects/invocation.png",
          "Pods": "https://dofusdb.fr/icons/effects/pod.png",
          "Familier": "https://dofusdb.fr/icons/effects/familier.png",
          "Liaison": "https://dofusdb.fr/icons/effects/liaison.png",

          // Additional stats that might appear
          "Épées Croisées": "https://dofusdb.fr/icons/effects/epeesCroisees.png",
          "Cadeau": "https://dofusdb.fr/icons/effects/cadeau.png"
        };

        // Function to get icon for a stat
        function getStatIcon(statText) {
          // First try exact match
          if (statIcons[statText]) {
            return statIcons[statText];
          }

          // Then try partial matches, but prioritize longer matches first
          const matches = [];
          for (const [key, iconUrl] of Object.entries(statIcons)) {
            if (statText.includes(key)) {
              matches.push({ key, iconUrl, length: key.length });
            }
          }

          // Sort by length (longest first) to prioritize more specific matches
          matches.sort((a, b) => b.length - a.length);

          // Return the longest match (most specific)
          return matches.length > 0 ? matches[0].iconUrl : null;
        }

        // Tiers and stats data
        const tiers = [
          {
            tierName: "Tier 1",
            stats: [
              { name: "Option 1", values: ["60 Force", "60 Intelligence", "60 Chance", "60 Agilité", "40 Puissance", "200 Vitalité", "700 Initiative"] },
            ]
          },
          {
            tierName: "Tier 2",
            stats: [
              { name: "Option 1", values: ["60 Force", "60 Intelligence", "60 Chance", "60 Agilité", "40 Puissance", "200 Vitalité", "700 Initiative"] },
              { name: "Option 2", values: ["20 Esquive PA", "20 Esquive PM", "20 Retrait PA", "20 Retrait PM", "40 Sagesse",
                                           "20 Tacle", "20 Fuite", "60 Prospection"] },
            ]
          },
          {
            tierName: "Tier 3",
            stats: [
              { name: "Option 1", values: ["60 Force", "60 Intelligence", "60 Chance", "60 Agilité", "40 Puissance", "200 Vitalité", "700 Initiative"] },
              { name: "Option 2", values: ["20 Esquive PA", "20 Esquive PM", "20 Retrait PA", "20 Retrait PM", "40 Sagesse",
                                           "20 Tacle", "20 Fuite", "60 Prospection"] },
              { name: "Option 3", values: ["7% Résistance Neutre", "7% Résistance Terre", "7% Résistance Feu", "7% Résistance Eau", "7% Résistance Air" ,
                                           "35 Résistance Neutre", "35 Résistance Terre", "35 Résistance Feu", "35 Résistance Eau", "35 Résistance Air" ,
                                           "40 Résistance Critique" , "40 Résistance Poussée"] },
            ]
          },
          {
            tierName: "Tier 4",
            stats: [
              { name: "Option 1", values: ["60 Force", "60 Intelligence", "60 Chance", "60 Agilité", "40 Puissance", "200 Vitalité", "700 Initiative"] },
              { name: "Option 2", values: ["20 Esquive PA", "20 Esquive PM", "20 Retrait PA", "20 Retrait PM", "40 Sagesse",
                                           "20 Tacle", "20 Fuite", "60 Prospection"] },
              { name: "Option 3", values: ["7% Résistance Neutre", "7% Résistance Terre", "7% Résistance Feu", "7% Résistance Eau", "7% Résistance Air" ,
                                           "35 Résistance Neutre", "35 Résistance Terre", "35 Résistance Feu", "35 Résistance Eau", "35 Résistance Air" ,
                                           "40 Résistance Critique" , "40 Résistance Poussée"] },
              { name: "Option 4", values: ["12 Dommages Neutre", "12 Dommages Terre","12 Dommages Feu","12 Dommages Eau","12 Dommages Air",
                                           "20 Dommages Critique", "40 Dommages Poussée",
                                           "7% Critique","25 Soins"] },
            ]
          },
          {
            tierName: "Tier 5",
            stats: [
              { name: "Option 1", values: ["60 Force", "60 Intelligence", "60 Chance", "60 Agilité", "40 Puissance", "200 Vitalité", "700 Initiative"] },
              { name: "Option 2", values: ["20 Esquive PA", "20 Esquive PM", "20 Retrait PA", "20 Retrait PM", "40 Sagesse",
                                           "20 Tacle", "20 Fuite", "60 Prospection"] },
              { name: "Option 3", values: ["7% Résistance Neutre", "7% Résistance Terre", "7% Résistance Feu", "7% Résistance Eau", "7% Résistance Air" ,
                                           "35 Résistance Neutre", "35 Résistance Terre", "35 Résistance Feu", "35 Résistance Eau", "35 Résistance Air" ,
                                           "40 Résistance Critique" , "40 Résistance Poussée"] },
              { name: "Option 4", values: ["12 Dommages Neutre", "12 Dommages Terre","12 Dommages Feu","12 Dommages Eau","12 Dommages Air",
                                           "20 Dommages Critique", "40 Dommages Poussée",
                                           "7% Critique","25 Soins"] },
              { name: "Option 5", values: ["1 PO", "1% Résistance aux Sorts", "3% Résistance d'Armes" ,"2% Résistance Distance","2% Résistance Mêlée",
                                           "1 Invocation", "1% Dommages aux Sorts", "3% Dommages d'Armes" ,"2% Dommages Distance","2% Dommages Mêlée" ] }
            ]
          }
        ];

        // Add S Tier and SS Tier
        tiers.push({
          tierName: "S Tier",
          stats: [
            { name: "Option 1", values: ["120 Force", "120 Intelligence", "120 Chance", "120 Agilité", "80 Puissance"] },
            { name: "Option 2", values: ["400 Vita", "1400 Initiative"] },
            { name: "Option 3", values: ["40 Esquive PA","40 Esquive PM", "40 Retrait PA","40 Retrait PM", "80 Sagesse"] },
            { name: "Option 4", values: ["40 Tacle", "40 Fuite", "100 Prospection"] },
            { name: "Option 5", values: ["15% Résistance Neutre", "15% Résistance Terre", "15% Résistance Feu", "15% Résistance Eau", "15% Résistance Air",
                                         "70 Résistance Neutre", "70 Résistance Terre", "70 Résistance Feu", "70 Résistance Eau", "70 Résistance Air"] },
            { name: "Option 6", values: ["80 Résistance Critique", "80 Résistance Poussée"] },
            { name: "Option 7", values: ["25 Dommages Neutre", "25 Dommages Terre","25 Dommages Feu","25 Dommages Eau","25 Dommages Air",
                                         "50 Dommages Critique", "80 Dommages Poussée"] },
            { name: "Option 8", values: ["15% Critique", "50 Soin"] },
            { name: "Option 9", values: ["2 PO", "3% Résistance aux Sorts", "7% Résistance d'Armes" ,"4% Résistance Distance","4% Résistance Mêlée"] },
            { name: "Option 10", values:["2 Invocation", "3% Dommages aux Sorts", "7% Dommages d'Armes" ,"4% Dommages Distance","4% Dommages Mêlée"] },
            { name: "Option 11", values:["1 PA", "1 PM"] }
          ]
        });

        tiers.push({
          tierName : "SS Tier",
          stats: [
            { name: "Option 1", values: ["100 Force", "100 Intelligence", "100 Chance", "100 Agilité", "60 Puissance"] },
            { name: "Option 2", values: ["300 Vita", "1000 Initiative"] },
            { name: "Option 3", values: ["30 Esquive PA","30 Esquive PM", "30 Retrait PA","30 Retrait PM", "60 Sagesse"] },
            { name: "Option 4", values: ["30 Tacle", "30 Fuite", "80 Prospection"] },
            { name: "Option 5", values: ["10% Résistance Neutre", "10% Résistance Terre", "10% Résistance Feu", "10% Résistance Eau", "10% Résistance Air",
                                         "50 Résistance Neutre", "50 Résistance Terre", "50 Résistance Feu", "50 Résistance Eau", "50 Résistance Air"] },
            { name: "Option 6", values: ["60 Résistance Critique", "60 Résistance Poussée"] },
            { name: "Option 7", values: ["20 Dommages Neutre", "20 Dommages Terre","20 Dommages Feu","20 Dommages Eau","20 Dommages Air",
                                         "30 Dommages Critique", "60 Dommages Poussée"] },
            { name: "Option 8", values: ["10% Critique", "35 Soin"] },
            { name: "Option 9", values: ["1 PO","2% Résistance aux Sorts", "5% Résistance d'Armes" ,"3% Résistance Distance","3% Résistance Mêlée"] },
            { name: "Option 10", values: ["1 Invocation", "2% Dommages aux Sorts", "5% Dommages d'Armes" ,"3% Dommages Distance","3% Dommages Mêlée",] },
            { name: "Option 11", values: ["1 PA", "1 PM"] }
          ]
        });

        

        // Maximum number of stats allowed for S Tier
        const tier6Labels = ["Option 1", "Option 2"];
        // Maximum number of stats allowed for SS Tier
        const tier7Labels = ["Option 1", "Option 2", "Option 3"];

        const MAX_STATS_S_TIER = 2;
        const MAX_STATS_SS_TIER = 3;

        // Global variables - Array where each index represents a tier
        let selectedStats = []; // Array of arrays, one for each tier

        // Initialize selectedStats array for all tiers
        function initializeSelectedStats() {
            selectedStats = new Array(7).fill(null).map(() => []);
        }

        // Load saved data on page load
        document.addEventListener('DOMContentLoaded', function() {
            initializeSelectedStats();
            loadSelectedStats();
            loadDarkMode();
            generateAccordionTiers();
            updateSelectedStatsList(); // Update the selected stats display after loading
            setupEventListeners();
        });

        function setupEventListeners() {
            // Dark mode toggle
            document.getElementById('darkModeToggle').addEventListener('click', toggleDarkMode);

            // Reset button
            document.getElementById('resetButton').addEventListener('click', resetAllStats);

            // Save/Load/Compare buttons
            document.getElementById('saveBuildBtn').addEventListener('click', saveBuild);
            document.getElementById('loadBuildBtn').addEventListener('click', loadBuild);
            document.getElementById('compareBtn').addEventListener('click', openCompareModal);

            // Search functionality
            const searchInput = document.getElementById('statSearch');
            const clearButton = document.getElementById('clearSearch');

            if (searchInput && clearButton) {
                searchInput.addEventListener('input', handleSearch);
                searchInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape') {
                        clearSearch();
                    }
                });

                clearButton.addEventListener('click', clearSearch);
            }

            // Clear highlighting when clicking outside the highlighted area
            document.addEventListener('click', function(e) {
                // Only clear if there's currently highlighting active
                const hasHighlighting = document.querySelector('.tier-content.highlighting');
                if (!hasHighlighting) return;

                // Don't clear if clicking on:
                // - Placeholder items (they trigger new highlighting)
                // - Tier headers in selected stats
                // - The highlighted option itself
                // - Any element inside the highlighted option
                if (!e.target.closest('.placeholder-item') &&
                    !e.target.closest('.tier-stats-header') &&
                    !e.target.closest('.option-group.highlighted')) {
                    clearOptionHighlighting();
                }
            });
        }

        // Generate accordion tiers
        function generateAccordionTiers() {
            const tiersAccordion = document.getElementById('tiersAccordion');
            tiersAccordion.innerHTML = '';

            tiers.forEach((tier, tierIndex) => {
                const tierSection = document.createElement('div');
                tierSection.className = 'tier-section';
                tierSection.setAttribute('data-tier-index', tierIndex);
                tierSection.innerHTML = `
                    <div class="tier-header" onclick="toggleTier(${tierIndex})">
                        <div class="tier-title">${tier.tierName}</div>
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <div class="tier-limit">Max: ${getTierLimit(tierIndex)}</div>
                            <div class="tier-arrow">▼</div>
                        </div>
                    </div>
                    <div class="tier-content" id="tier-content-${tierIndex}">
                        <div id="tier-stats-${tierIndex}">
                            ${generateStatsForTier(tier, tierIndex)}
                        </div>
                    </div>
                `;
                tiersAccordion.appendChild(tierSection);
            });
        }

        function toggleTier(tierIndex) {
            const content = document.getElementById(`tier-content-${tierIndex}`);
            const header = content.previousElementSibling;
            const arrow = header.querySelector('.tier-arrow');

            // Toggle current tier only (don't close others)
            const isOpen = content.style.display === 'block';
            if (isOpen) {
                content.style.display = 'none';
                arrow.textContent = '▶';
                header.parentElement.classList.remove('active');
            } else {
                content.style.display = 'block';
                arrow.textContent = '▼';
                header.parentElement.classList.add('active');
            }
        }

        function getTierLimit(tierIndex) {
            if (tierIndex === 5) return MAX_STATS_S_TIER; // S Tier
            if (tierIndex === 6) return MAX_STATS_SS_TIER; // SS Tier
            return 1; // Regular tiers
        }

        function generateStatsForTier(tier, tierIndex) {
            let html = '';
            tier.stats.forEach((option, optionIndex) => {
                html += `
                    <div class="tier-container option-group">
                        <h3>${option.name}</h3>
                        <div class="stat-group">
                            ${option.values.map(stat => {
                                const isSelected = isStatSelected(tierIndex, optionIndex, stat);
                                const iconUrl = getStatIcon(stat);
                                const iconHtml = iconUrl ? `<img src="${iconUrl}" alt="${stat}" class="stat-icon" onerror="this.style.display='none'">` : '';

                                return `
                                    <button class="stat-button ${isSelected ? 'selected' : ''}"
                                            onclick="selectStat(${tierIndex}, ${optionIndex}, '${stat.replace(/'/g, "\\'")}')"
                                            data-stat="${stat}"
                                            data-group="${optionIndex}">
                                        ${iconHtml}
                                        <span>${stat}</span>
                                    </button>
                                `;
                            }).join('')}
                        </div>
                    </div>
                `;
            });
            return html;
        }

        function isStatSelected(tierIndex, optionIndex, stat) {
            if (tierIndex === 5 || tierIndex === 6) { // S Tier or SS Tier
                return selectedStats[tierIndex]?.some(entry =>
                    entry.stat === stat && entry.groupIndex === optionIndex
                );
            }
            return selectedStats[tierIndex]?.[optionIndex] === stat;
        }

        function updateTierButtonStates(tierIndex) {
            const tierContainer = document.getElementById(`tier-stats-${tierIndex}`);
            if (!tierContainer) return;

            const buttons = tierContainer.querySelectorAll('.stat-button');
            buttons.forEach(button => {
                const stat = button.getAttribute('data-stat');
                const groupIndex = parseInt(button.getAttribute('data-group'));
                const isSelected = isStatSelected(tierIndex, groupIndex, stat);

                if (isSelected) {
                    button.classList.add('selected');
                } else {
                    button.classList.remove('selected');
                }
            });
        }

        function selectStat(tierIndex, groupIndex, stat) {
            if (tierIndex === 5 || tierIndex === 6) { // S Tier or SS Tier
                handleSTierSelection(tierIndex, groupIndex, stat);
            } else { // Regular tiers (1-5)
                handleRegularTierSelection(tierIndex, groupIndex, stat);
            }

            updateTierButtonStates(tierIndex); // Only update button states for this tier
            updateSelectedStatsList();
            saveSelectedStats();
        }

        function handleRegularTierSelection(tierIndex, groupIndex, stat) {
            // For regular tiers, only one option can be selected
            if (selectedStats[tierIndex][groupIndex] === stat) {
                // Deselect if clicking the same stat
                selectedStats[tierIndex][groupIndex] = null;
            } else {
                // Select the new stat
                selectedStats[tierIndex][groupIndex] = stat;
            }
        }

        function handleSTierSelection(tierIndex, groupIndex, stat) {
            const maxAllowed = getTierLimit(tierIndex);
            const currentStats = selectedStats[tierIndex] || [];

            // Check if this stat is already selected
            const existingIndex = currentStats.findIndex(entry =>
                entry.stat === stat && entry.groupIndex === groupIndex
            );

            if (existingIndex !== -1) {
                // Remove the stat
                selectedStats[tierIndex].splice(existingIndex, 1);
                return;
            }

            // Check if there's already a stat selected from this option (groupIndex)
            const existingFromSameOption = currentStats.findIndex(entry =>
                entry.groupIndex === groupIndex
            );

            if (existingFromSameOption !== -1) {
                // Replace the existing stat from this option
                selectedStats[tierIndex][existingFromSameOption] = {
                    stat: stat,
                    groupIndex: groupIndex
                };
                return;
            }

            // Check if we're at the limit
            if (currentStats.length >= maxAllowed) {
                showTierModal(tierIndex, groupIndex, stat);
                return;
            }

            // Add the stat
            if (!selectedStats[tierIndex]) {
                selectedStats[tierIndex] = [];
            }
            selectedStats[tierIndex].push({
                stat: stat,
                groupIndex: groupIndex
            });
        }

        function showTierModal(tierIndex, groupIndex, stat) {
            const modal = document.getElementById('tierModal');
            const replaceOptions = document.getElementById('replaceOptions');

            const tierStats = selectedStats[tierIndex] || [];

            replaceOptions.innerHTML = tierStats.map((entry, index) => `
                <button class="btn" style="margin: 5px; width: 100%;"
                        onclick="replaceStat(${tierIndex}, ${groupIndex}, '${stat.replace(/'/g, "\\'")}', ${index})">
                    Remplacer: ${entry.stat}
                </button>
            `).join('');

            modal.style.display = 'block';
        }

        function replaceStat(tierIndex, groupIndex, newStat, replaceIndex) {
            // Remove the old stat
            selectedStats[tierIndex].splice(replaceIndex, 1);

            // Add the new stat
            selectedStats[tierIndex].push({
                stat: newStat,
                groupIndex: groupIndex
            });

            closeTierModal();
            updateTierButtonStates(tierIndex); // Only update button states for this tier
            updateSelectedStatsList();
            saveSelectedStats();
        }

        function closeTierModal() {
            document.getElementById('tierModal').style.display = 'none';
        }

        function updateSelectedStatsList() {
            const selectedList = document.getElementById('selectedStatsList');

            // Group stats by tier for display
            let hasAnyStats = false;
            let html = '';

            selectedStats.forEach((tierStats, tierIndex) => {
                const tierName = tiers[tierIndex].tierName;
                const tierSelectedStats = [];

                if (tierIndex === 5 || tierIndex === 6) { // S Tier or SS Tier
                    if (tierStats && Array.isArray(tierStats) && tierStats.length > 0) {
                        tierStats.forEach(entry => {
                            tierSelectedStats.push({
                                stat: entry.stat,
                                groupIndex: entry.groupIndex
                            });
                        });
                    }
                } else { // Regular tiers
                    if (tierStats && Array.isArray(tierStats)) {
                        tierStats.forEach((stat, groupIndex) => {
                            if (stat) {
                                tierSelectedStats.push({
                                    stat: stat,
                                    groupIndex: groupIndex
                                });
                            }
                        });
                    }
                }

                if (tierSelectedStats.length > 0 || shouldShowTierPlaceholder(tierIndex)) {
                    hasAnyStats = true;

                    // Generate selected stats HTML
                    const selectedStatsHtml = tierSelectedStats.map(statInfo => {
                        const iconUrl = getStatIcon(statInfo.stat);
                        const iconHtml = iconUrl ? `<img src="${iconUrl}" alt="${statInfo.stat}" class="stat-icon" onerror="this.style.display='none'">` : '';

                        return `
                            <div class="selected-item">
                                <div class="selected-info">
                                    ${iconHtml}
                                    <span>${statInfo.stat}</span>
                                </div>
                                <button class="remove-btn" onclick="removeStatFromList(${tierIndex}, ${statInfo.groupIndex}, '${statInfo.stat.replace(/'/g, "\\'")}')">×</button>
                            </div>
                        `;
                    }).join('');

                    // Generate placeholders for remaining slots
                    const placeholdersHtml = generatePlaceholders(tierIndex, tierSelectedStats.length);

                    html += `
                        <div class="tier-stats-group">
                            <div class="tier-stats-header" onclick="openTierFromSelectedStats(${tierIndex})" title="Click to open this tier">${tierName}</div>
                            <div class="tier-stats-content">
                                ${selectedStatsHtml}
                                ${placeholdersHtml}
                            </div>
                        </div>
                    `;
                }
            });

            if (!hasAnyStats) {
                selectedList.innerHTML = `
                    <div style="text-align: center; opacity: 0.6; padding: 20px;">
                        Aucune statistique sélectionnée.<br>
                        <small>Cliquez sur les statistiques des niveaux pour les ajouter !</small>
                    </div>
                `;
            } else {
                selectedList.innerHTML = html;
            }
        }

        function shouldShowTierPlaceholder(tierIndex) {
            // Show placeholder if tier has any selections or if it's a tier that allows multiple selections
            const tierStats = selectedStats[tierIndex];
            if (!tierStats) return false;

            if (tierIndex === 5 || tierIndex === 6) { // S Tier or SS Tier
                return tierStats.length > 0 && tierStats.length < getTierLimit(tierIndex);
            } else { // Regular tiers
                // Show placeholder if any option is selected but not all options are filled
                const hasAnySelection = tierStats.some(stat => stat !== null && stat !== undefined);
                const maxOptions = tiers[tierIndex].stats.length;
                const filledOptions = tierStats.filter(stat => stat !== null && stat !== undefined).length;
                return hasAnySelection && filledOptions < maxOptions;
            }
        }

        function generatePlaceholders(tierIndex, currentCount) {
            let placeholders = '';

            if (tierIndex === 5 || tierIndex === 6) { // S Tier or SS Tier
                const maxAllowed = getTierLimit(tierIndex);
                const remaining = maxAllowed - currentCount;

                for (let i = 0; i < remaining; i++) {
                    placeholders += `
                        <div class="placeholder-item" onclick="openTierFromSelectedStats(${tierIndex})" title="Click to select a characteristic">
                            <div class="placeholder-info">
                                <span class="placeholder-icon">📊</span>
                                <span class="placeholder-text">Caractéristique disponible</span>
                            </div>
                        </div>
                    `;
                }
            } else { // Regular tiers
                const tierStats = selectedStats[tierIndex] || [];
                const maxOptions = tiers[tierIndex].stats.length;

                // Show placeholders for empty option slots
                for (let i = 0; i < maxOptions; i++) {
                    if (!tierStats[i]) {
                        placeholders += `
                            <div class="placeholder-item" onclick="openTierAndHighlightOption(${tierIndex}, ${i})" title="Click to select from Option ${i + 1}">
                                <div class="placeholder-info">
                                    <span class="placeholder-icon">📊</span>
                                    <span class="placeholder-text">Option ${i + 1} disponible</span>
                                </div>
                            </div>
                        `;
                    }
                }
            }

            return placeholders;
        }

        function openTierFromSelectedStats(tierIndex) {
            // Close all other tiers first
            const allTierSections = document.querySelectorAll('.tier-section');
            allTierSections.forEach(section => {
                const content = section.querySelector('.tier-content');
                const header = section.querySelector('.tier-header');
                const arrow = header.querySelector('.tier-arrow');

                content.style.display = 'none';
                arrow.textContent = '▶';
                section.classList.remove('active');
            });

            // Open the specific tier
            const targetTierSection = document.querySelector(`[data-tier-index="${tierIndex}"]`);
            if (targetTierSection) {
                const content = targetTierSection.querySelector('.tier-content');
                const header = targetTierSection.querySelector('.tier-header');
                const arrow = header.querySelector('.tier-arrow');

                content.style.display = 'block';
                arrow.textContent = '▼';
                targetTierSection.classList.add('active');

                // Scroll to the tier
                targetTierSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }

        function openTierAndHighlightOption(tierIndex, optionIndex) {
            // Clear any existing highlighting first
            clearOptionHighlighting();

            // Check if tier is already open
            const targetTierSection = document.querySelector(`[data-tier-index="${tierIndex}"]`);
            const isAlreadyOpen = targetTierSection && targetTierSection.querySelector('.tier-content').style.display === 'block';

            if (isAlreadyOpen) {
                // Tier is already open, just highlight and scroll smoothly
                highlightOptionDirectly(tierIndex, optionIndex);
            } else {
                // Close all other tiers first
                const allTierSections = document.querySelectorAll('.tier-section');
                allTierSections.forEach(section => {
                    const content = section.querySelector('.tier-content');
                    const header = section.querySelector('.tier-header');
                    const arrow = header.querySelector('.tier-arrow');

                    content.style.display = 'none';
                    arrow.textContent = '▶';
                    section.classList.remove('active');
                });

                // Open the specific tier
                if (targetTierSection) {
                    const content = targetTierSection.querySelector('.tier-content');
                    const header = targetTierSection.querySelector('.tier-header');
                    const arrow = header.querySelector('.tier-arrow');

                    content.style.display = 'block';
                    arrow.textContent = '▼';
                    targetTierSection.classList.add('active');

                    // Immediately highlight and scroll in one smooth motion
                    highlightOptionDirectly(tierIndex, optionIndex);
                }
            }
        }

        function highlightOptionDirectly(tierIndex, optionIndex) {
            const targetTierSection = document.querySelector(`[data-tier-index="${tierIndex}"]`);
            if (targetTierSection) {
                const tierContent = targetTierSection.querySelector('.tier-content');
                const optionGroups = tierContent.querySelectorAll('.option-group');

                // Add highlighting class to tier content
                tierContent.classList.add('highlighting');

                // Highlight the specific option
                if (optionGroups[optionIndex]) {
                    optionGroups[optionIndex].classList.add('highlighted');

                    // Scroll to the highlighted option smoothly
                    optionGroups[optionIndex].scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                }
            }
        }

        function clearOptionHighlighting() {
            // Remove all highlighting classes
            document.querySelectorAll('.tier-content').forEach(content => {
                content.classList.remove('highlighting');
            });
            document.querySelectorAll('.option-group').forEach(group => {
                group.classList.remove('highlighted', 'dimmed');
            });
        }

        function removeStatFromList(tierIndex, groupIndex, stat) {
            if (tierIndex === 5 || tierIndex === 6) { // S Tier or SS Tier
                const statIndex = selectedStats[tierIndex].findIndex(entry =>
                    entry.stat === stat && entry.groupIndex === groupIndex
                );
                if (statIndex !== -1) {
                    selectedStats[tierIndex].splice(statIndex, 1);
                }
            } else { // Regular tiers
                selectedStats[tierIndex][groupIndex] = null;
            }

            updateTierButtonStates(tierIndex); // Only update button states for this tier
            updateSelectedStatsList();
            saveSelectedStats();
        }



        function handleSearch() {
            const searchTerm = document.getElementById('statSearch').value.toLowerCase().trim();
            const clearButton = document.getElementById('clearSearch');
            const statButtons = document.querySelectorAll('.stat-button');

            clearButton.style.display = searchTerm ? 'block' : 'none';

            statButtons.forEach(button => {
                const statText = button.getAttribute('data-stat').toLowerCase();
                const isMatch = statText.includes(searchTerm);

                button.classList.toggle('hidden', !isMatch);

                if (isMatch && searchTerm) {
                    highlightSearchTerm(button, searchTerm);
                } else {
                    removeHighlight(button);
                }
            });
        }

        function highlightSearchTerm(button, searchTerm) {
            const textSpan = button.querySelector('span');
            const originalText = button.getAttribute('data-stat');
            const regex = new RegExp(`(${searchTerm})`, 'gi');
            const highlightedText = originalText.replace(regex, '<span class="highlight">$1</span>');
            textSpan.innerHTML = highlightedText;
        }

        function removeHighlight(button) {
            const textSpan = button.querySelector('span');
            textSpan.innerHTML = textSpan.textContent;
        }

        function clearSearch() {
            const searchInput = document.getElementById('statSearch');
            searchInput.value = '';
            handleSearch();
            searchInput.focus();
        }

        function toggleDarkMode() {
            const body = document.body;
            const isDark = body.getAttribute('data-theme') === 'dark';
            const newTheme = isDark ? 'light' : 'dark';

            body.setAttribute('data-theme', newTheme);
            localStorage.setItem('darkMode', newTheme);

            const toggleBtn = document.getElementById('darkModeToggle');
            toggleBtn.textContent = isDark ? '🌙 Sombre' : '☀️ lumière';
        }

        function loadDarkMode() {
            const savedTheme = localStorage.getItem('darkMode') || 'light';
            document.body.setAttribute('data-theme', savedTheme);

            const toggleBtn = document.getElementById('darkModeToggle');
            toggleBtn.textContent = savedTheme === 'dark' ? '☀️ lumière' : '🌙 Sombre';
        }

        function saveSelectedStats() {
            localStorage.setItem('selectedStats', JSON.stringify(selectedStats));
        }

        function loadSelectedStats() {
            const saved = localStorage.getItem('selectedStats');
            if (saved) {
                selectedStats = JSON.parse(saved);
            }
        }

        function resetAllStats() {
            if (confirm('Are you sure you want to reset all selected stats?')) {
                initializeSelectedStats();
                // Update button states for all tiers
                tiers.forEach((_, tierIndex) => {
                    updateTierButtonStates(tierIndex);
                });
                updateSelectedStatsList();
                saveSelectedStats();
            }
        }

        // Save/Load/Compare System
        function getSavedBuilds() {
            const saved = localStorage.getItem('dofus-saved-builds');
            return saved ? JSON.parse(saved) : [];
        }

        function saveBuild() {
            openSaveModal();
        }

        function openSaveModal() {
            const modal = document.createElement('div');
            modal.id = 'saveModal';
            modal.innerHTML = `
                <div class="modal-overlay" onclick="closeSaveModal()">
                    <div class="modal-content save-modal" onclick="event.stopPropagation()">
                        <div class="modal-header">
                            <h2>💾 Sauvegarder Suivline</h2>
                            <button class="modal-close" onclick="closeSaveModal()">✕</button>
                        </div>
                        <div class="modal-body">
                            <div class="save-form">
                                <div class="form-group">
                                    <label for="suivlineName">Nom de la Suivline:</label>
                                    <input type="text" id="suivlineName" placeholder="Ex: Tank Build, DPS Mage..." value="Suivline ${new Date().toLocaleDateString()}">
                                </div>

                                <div class="form-group">
                                    <label>Tiers à sauvegarder:</label>
                                    <div class="tier-selection">
                                        <label class="tier-option">
                                            <input type="radio" name="tierSelection" value="all" checked>
                                            <span>Tous les tiers</span>
                                        </label>
                                        ${tiers.map((tier, index) => {
                                            const hasStats = tierHasStats(selectedStats[index], index);
                                            return `
                                                <label class="tier-option ${!hasStats ? 'disabled' : ''}">
                                                    <input type="radio" name="tierSelection" value="${index}" ${!hasStats ? 'disabled' : ''}>
                                                    <span>${tier.tierName} ${!hasStats ? '(vide)' : ''}</span>
                                                </label>
                                            `;
                                        }).join('')}
                                    </div>
                                </div>

                                <div class="save-actions">
                                    <button class="btn btn-primary" onclick="performSave()">💾 Sauvegarder</button>
                                    <button class="btn" onclick="closeSaveModal()">Annuler</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        function performSave() {
            const nameInput = document.getElementById('suivlineName');
            const selectedTier = document.querySelector('input[name="tierSelection"]:checked').value;
            const suivlineName = nameInput.value.trim();

            if (!suivlineName) {
                showNotification('Veuillez entrer un nom pour la Suivline', 'error');
                return;
            }

            let dataToSave;
            if (selectedTier === 'all') {
                dataToSave = [...selectedStats];
            } else {
                const tierIndex = parseInt(selectedTier);
                dataToSave = selectedStats.map((tierStats, index) =>
                    index === tierIndex ? tierStats : []
                );
            }

            const suivlineData = {
                id: Date.now().toString(),
                name: suivlineName,
                timestamp: new Date().toISOString(),
                selectedStats: dataToSave,
                tierNames: tiers.map(tier => tier.tierName),
                savedTier: selectedTier === 'all' ? 'all' : tiers[parseInt(selectedTier)].tierName
            };

            const savedSuivlines = getSavedBuilds();
            savedSuivlines.push(suivlineData);
            localStorage.setItem('dofus-saved-builds', JSON.stringify(savedSuivlines));

            closeSaveModal();
            showNotification(`Suivline "${suivlineName}" sauvegardée!`, 'success');
        }

        function closeSaveModal() {
            const modal = document.getElementById('saveModal');
            if (modal) modal.remove();
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <span class="notification-icon">${type === 'success' ? '✓' : type === 'error' ? '✗' : 'ℹ'}</span>
                    <span class="notification-message">${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        function loadBuild() {
            openSuivlinesPage();
        }

        function openSuivlinesPage() {
            const savedSuivlines = getSavedBuilds();

            const modal = document.createElement('div');
            modal.id = 'suivlinesPage';
            modal.innerHTML = `
                <div class="modal-overlay" onclick="closeSuivlinesPage()">
                    <div class="modal-content suivlines-page" onclick="event.stopPropagation()">
                        <div class="modal-header">
                            <h2>📂 Mes Suivlines</h2>
                            <button class="modal-close" onclick="closeSuivlinesPage()">✕</button>
                        </div>
                        <div class="modal-body">
                            ${savedSuivlines.length === 0 ? `
                                <div class="empty-state">
                                    <div class="empty-icon">📦</div>
                                    <h3>Aucune Suivline sauvegardée</h3>
                                    <p>Créez votre première Suivline en sélectionnant des statistiques et en cliquant sur "Sauvegarder"</p>
                                </div>
                            ` : `
                                <div class="suivlines-grid">
                                    ${savedSuivlines.map((suivline, index) => createSuivlineCard(suivline, index)).join('')}
                                </div>
                            `}
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        function createSuivlineCard(suivline, index) {
            const date = new Date(suivline.timestamp).toLocaleDateString();
            const statsCount = countSuivlineStats(suivline.selectedStats);

            return `
                <div class="suivline-card" data-index="${index}">
                    <div class="suivline-header">
                        <div class="suivline-icon">⚔️</div>
                        <div class="suivline-info">
                            <div class="suivline-name">${suivline.name}</div>
                            <div class="suivline-meta">
                                <span class="suivline-date">${date}</span>
                                <span class="suivline-stats">${statsCount} stats</span>
                            </div>
                        </div>
                    </div>
                    <div class="suivline-preview">
                        ${createSuivlinePreview(suivline)}
                    </div>
                    <div class="suivline-actions">
                        <button class="btn btn-primary" onclick="loadSuivline(${index})">
                            📂 Charger
                        </button>
                        <button class="btn btn-secondary" onclick="duplicateSuivline(${index})">
                            📋 Dupliquer
                        </button>
                        <button class="btn btn-danger" onclick="deleteSuivline(${index})">
                            🗑️ Supprimer
                        </button>
                    </div>
                </div>
            `;
        }

        function createSuivlinePreview(suivline) {
            let preview = '';
            suivline.selectedStats.forEach((tierStats, tierIndex) => {
                const tierStats_filtered = extractStatsFromTier(tierStats, tierIndex);
                if (tierStats_filtered.length > 0) {
                    const tierName = suivline.tierNames[tierIndex];
                    preview += `
                        <div class="tier-preview">
                            <div class="tier-preview-name">${tierName}</div>
                            <div class="tier-preview-stats">
                                ${tierStats_filtered.slice(0, 3).map(stat => `
                                    <span class="stat-preview">${stat}</span>
                                `).join('')}
                                ${tierStats_filtered.length > 3 ? `<span class="stat-more">+${tierStats_filtered.length - 3}</span>` : ''}
                            </div>
                        </div>
                    `;
                }
            });
            return preview || '<div class="no-stats">Aucune statistique</div>';
        }

        function countSuivlineStats(selectedStats) {
            let count = 0;
            selectedStats.forEach((tierStats, tierIndex) => {
                if (tierIndex === 5 || tierIndex === 6) { // S Tier or SS Tier
                    if (Array.isArray(tierStats)) {
                        count += tierStats.filter(entry => entry && entry.stat).length;
                    }
                } else { // Regular tiers
                    if (Array.isArray(tierStats)) {
                        count += tierStats.filter(stat => stat).length;
                    }
                }
            });
            return count;
        }

        function extractStatsFromTier(tierStats, tierIndex) {
            if (tierIndex === 5 || tierIndex === 6) { // S Tier or SS Tier
                return Array.isArray(tierStats) ? tierStats.filter(entry => entry && entry.stat).map(entry => entry.stat) : [];
            } else { // Regular tiers
                return Array.isArray(tierStats) ? tierStats.filter(stat => stat) : [];
            }
        }

        function tierHasStats(tierStats, tierIndex) {
            if (tierIndex === 5 || tierIndex === 6) { // S Tier or SS Tier
                return Array.isArray(tierStats) && tierStats.some(entry => entry && entry.stat);
            } else { // Regular tiers
                return Array.isArray(tierStats) && tierStats.some(stat => stat);
            }
        }

        function loadSuivline(index) {
            const savedSuivlines = getSavedBuilds();
            const suivline = savedSuivlines[index];

            selectedStats = [...suivline.selectedStats];
            generateAccordionTiers();
            updateSelectedStatsList();
            saveSelectedStats();

            closeSuivlinesPage();
            showNotification(`Suivline "${suivline.name}" chargée!`, 'success');
        }

        function duplicateSuivline(index) {
            const savedSuivlines = getSavedBuilds();
            const originalSuivline = savedSuivlines[index];

            const duplicatedSuivline = {
                ...originalSuivline,
                id: Date.now().toString(),
                name: `${originalSuivline.name} (Copie)`,
                timestamp: new Date().toISOString()
            };

            savedSuivlines.push(duplicatedSuivline);
            localStorage.setItem('dofus-saved-builds', JSON.stringify(savedSuivlines));

            // Refresh the page
            closeSuivlinesPage();
            setTimeout(() => openSuivlinesPage(), 100);
            showNotification(`Suivline dupliquée!`, 'success');
        }

        function deleteSuivline(index) {
            const savedSuivlines = getSavedBuilds();
            const suivline = savedSuivlines[index];

            // Create confirmation modal
            const confirmModal = document.createElement('div');
            confirmModal.id = 'confirmModal';
            confirmModal.innerHTML = `
                <div class="modal-overlay" onclick="closeConfirmModal()">
                    <div class="modal-content confirm-modal" onclick="event.stopPropagation()">
                        <div class="modal-header">
                            <h2>🗑️ Confirmer la suppression</h2>
                        </div>
                        <div class="modal-body">
                            <p>Êtes-vous sûr de vouloir supprimer la Suivline <strong>"${suivline.name}"</strong> ?</p>
                            <p class="warning-text">Cette action est irréversible.</p>
                            <div class="confirm-actions">
                                <button class="btn btn-danger" onclick="confirmDeleteSuivline(${index})">
                                    🗑️ Supprimer
                                </button>
                                <button class="btn" onclick="closeConfirmModal()">
                                    Annuler
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(confirmModal);
        }

        function confirmDeleteSuivline(index) {
            const savedSuivlines = getSavedBuilds();
            const suivline = savedSuivlines[index];

            savedSuivlines.splice(index, 1);
            localStorage.setItem('dofus-saved-builds', JSON.stringify(savedSuivlines));

            closeConfirmModal();
            closeSuivlinesPage();
            setTimeout(() => openSuivlinesPage(), 100);
            showNotification(`Suivline "${suivline.name}" supprimée!`, 'success');
        }

        function closeConfirmModal() {
            const modal = document.getElementById('confirmModal');
            if (modal) modal.remove();
        }

        function closeSuivlinesPage() {
            const modal = document.getElementById('suivlinesPage');
            if (modal) modal.remove();
        }

        function openCompareModal() {
            const savedSuivlines = getSavedBuilds();
            if (savedSuivlines.length === 0) {
                showNotification('Aucune Suivline sauvegardée pour comparer.', 'error');
                return;
            }

            // Create comparison modal
            createComparisonModal(savedSuivlines);
        }

        function createComparisonModal(savedSuivlines) {
            // Remove existing modal if any
            const existingModal = document.getElementById('compareModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Sort by latest first
            const sortedSuivlines = [...savedSuivlines].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

            // Create modal HTML
            const modal = document.createElement('div');
            modal.id = 'compareModal';
            modal.innerHTML = `
                <div class="modal-overlay" onclick="closeCompareModal()">
                    <div class="modal-content comparison-modal" onclick="event.stopPropagation()">
                        <div class="modal-header">
                            <h2>⚖️ Comparer les Suivlines</h2>
                            <button class="modal-close" onclick="closeCompareModal()">✕</button>
                        </div>
                        <div class="modal-body">
                            <div class="comparison-controls">
                                <div class="search-section">
                                    <input type="text" id="suivlineSearch" placeholder="🔍 Rechercher une Suivline..." onkeyup="filterSuivlines()">
                                </div>
                                <div class="view-controls">
                                    <button class="btn btn-secondary" id="toggleViewBtn" onclick="toggleBuildView()">
                                        📋 Vue détaillée
                                    </button>
                                    <span class="results-count" id="resultsCount">${sortedSuivlines.length} Suivlines</span>
                                </div>
                            </div>

                            <div class="build-selector">
                                <h3>Sélectionnez les éléments à comparer:</h3>
                                <div class="build-list" id="buildList">
                                    ${createBuildListHTML(sortedSuivlines, 0, 6)}
                                </div>
                                <div class="pagination-controls" id="paginationControls">
                                    ${createPaginationHTML(sortedSuivlines.length, 0, 6)}
                                </div>
                                <div class="compare-actions">
                                    <button class="btn btn-primary" id="compareSelectedBtn" onclick="compareSelectedBuilds()" disabled>
                                        Comparer les sélectionnées
                                    </button>
                                    <div class="selection-info" id="selectionInfo">
                                        Sélectionnez au moins 2 éléments pour comparer
                                    </div>
                                </div>
                            </div>
                            <div class="comparison-results" id="comparisonResults"></div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Store sorted data globally for pagination and search
            window.currentSuivlines = sortedSuivlines;
            window.currentPage = 0;
            window.itemsPerPage = 6;
            window.detailedView = false;
            window.selectedItems = new Set(); // Track selected items across page changes
        }

        function createBuildListHTML(suivlines, startIndex, itemsPerPage) {
            const endIndex = Math.min(startIndex + itemsPerPage, suivlines.length);
            let html = '';

            // Get the original saved builds array for proper index mapping
            const allSavedBuilds = getSavedBuilds();

            for (let i = startIndex; i < endIndex; i++) {
                const suivline = suivlines[i];
                // Find the original index in the saved builds array using the unique id
                const originalIndex = allSavedBuilds.findIndex(s => s.id === suivline.id);

                if (window.detailedView) {
                    html += createDetailedBuildItem(suivline, originalIndex);
                } else {
                    html += createCompactBuildItem(suivline, originalIndex);
                }
            }

            return html;
        }

        function createCompactBuildItem(suivline, index) {
            const date = new Date(suivline.timestamp).toLocaleDateString();
            const statsCount = countSuivlineStats(suivline.selectedStats);
            const isRecent = (Date.now() - new Date(suivline.timestamp)) < 24 * 60 * 60 * 1000; // Last 24h

            return `
                <div class="build-item compact clickable" data-index="${index}" onclick="toggleBuildSelection('build-${index}', event)">
                    <div class="build-checkbox">
                        <input type="checkbox" value="build-${index}" onchange="updateCompareSelection()">
                    </div>
                    <div class="build-preview">
                        <div class="build-header">
                            <div class="build-name">
                                ${suivline.name}
                                ${isRecent ? '<span class="recent-badge">Nouveau</span>' : ''}
                            </div>
                            <div class="build-meta">
                                <span class="build-date">📅 ${date}</span>
                                <span class="build-stats">⚔️ ${statsCount} stats</span>
                            </div>
                        </div>
                        <div class="build-quick-preview">
                            ${createCompactQuickPreview(suivline)}
                        </div>
                    </div>
                    <div class="build-actions">
                        <button class="btn btn-small" onclick="toggleBuildDetails(${index}, event)">
                            👁️ Détails
                        </button>
                    </div>
                </div>
            `;
        }

        function createDetailedBuildItem(suivline, index) {
            const date = new Date(suivline.timestamp).toLocaleDateString();
            const statsCount = countSuivlineStats(suivline.selectedStats);
            const isRecent = (Date.now() - new Date(suivline.timestamp)) < 24 * 60 * 60 * 1000;

            return `
                <div class="build-item detailed" data-index="${index}">
                    <div class="build-header-detailed">
                        <div class="build-checkbox">
                            <input type="checkbox" value="build-${index}" onchange="updateCompareSelection()">
                        </div>
                        <div class="build-info-detailed clickable" onclick="toggleBuildSelection('build-${index}', event)">
                            <div class="build-name">
                                ${suivline.name}
                                ${isRecent ? '<span class="recent-badge">Nouveau</span>' : ''}
                            </div>
                            <div class="build-meta">
                                <span class="build-date">📅 ${date}</span>
                                <span class="build-stats">⚔️ ${statsCount} stats</span>
                            </div>
                        </div>
                        <div class="build-actions">
                            <button class="btn btn-small" onclick="toggleBuildDetails(${index}, event)">
                                📋 Compact
                            </button>
                        </div>
                    </div>
                    <div class="tier-selection-grid">
                        ${createTierSelectionGrid(suivline, index)}
                    </div>
                </div>
            `;
        }

        function createCompactQuickPreview(suivline) {
            const tierPreviews = [];
            suivline.selectedStats.forEach((tierStats, tierIndex) => {
                const tierStatsExtracted = extractStatsFromTier(tierStats, tierIndex);
                if (tierStatsExtracted.length > 0) {
                    const tierName = suivline.tierNames[tierIndex];
                    tierPreviews.push(`${tierName}(${tierStatsExtracted.length})`);
                }
            });

            if (tierPreviews.length === 0) {
                return '<span class="no-preview">Aucune statistique</span>';
            }

            return `
                <div class="compact-preview-items">
                    ${tierPreviews.join(' • ')}
                </div>
            `;
        }

        function toggleBuildSelection(value, event) {
            if (event) event.stopPropagation();
            const checkbox = document.querySelector(`input[value="${value}"]`);
            if (checkbox) {
                checkbox.checked = !checkbox.checked;
                updateCompareSelection();
            }
        }

        function toggleTierSelection(value, event) {
            if (event) event.stopPropagation();
            const checkbox = document.querySelector(`input[value="${value}"]`);
            if (checkbox && !checkbox.disabled) {
                checkbox.checked = !checkbox.checked;
                updateCompareSelection();
            }
        }

        function createTierSelectionGrid(suivline, buildIndex) {
            let html = '<div class="tier-grid-header">Sélectionner par tier:</div>';
            html += '<div class="tier-grid">';

            suivline.selectedStats.forEach((tierStats, tierIndex) => {
                const tierStatsExtracted = extractStatsFromTier(tierStats, tierIndex);
                const tierName = suivline.tierNames[tierIndex];
                const hasStats = tierStatsExtracted.length > 0;
                const tierValue = `tier-${buildIndex}-${tierIndex}`;

                html += `
                    <div class="tier-selection-item ${!hasStats ? 'disabled' : 'clickable'}"
                         onclick="${hasStats ? `toggleTierSelection('${tierValue}', event)` : ''}">
                        <div class="tier-checkbox">
                            <input type="checkbox"
                                   value="${tierValue}"
                                   onchange="updateCompareSelection()"
                                   ${!hasStats ? 'disabled' : ''}>
                        </div>
                        <div class="tier-info">
                            <div class="tier-name">${tierName} ${hasStats ? `(${tierStatsExtracted.length})` : '(vide)'}</div>
                            <div class="tier-stats-preview">
                                ${hasStats ?
                                    tierStatsExtracted.slice(0, 3).join(' • ') +
                                    (tierStatsExtracted.length > 3 ? ` • +${tierStatsExtracted.length - 3}` : '')
                                    : '<span class="no-tier-stats">Aucune stat</span>'
                                }
                            </div>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            return html;
        }

        function createPaginationHTML(totalItems, currentPage, itemsPerPage) {
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            if (totalPages <= 1) return '';

            let html = '<div class="pagination">';

            // Previous button - always show but disable if needed
            const prevDisabled = currentPage === 0;
            html += `<button class="btn btn-small ${prevDisabled ? 'disabled' : ''}"
                     onclick="${prevDisabled ? '' : `changePage(${currentPage - 1})`}">‹ Précédent</button>`;

            // Show all page numbers
            for (let i = 0; i < totalPages; i++) {
                html += `<button class="btn btn-small ${i === currentPage ? 'active' : ''}" onclick="changePage(${i})">${i + 1}</button>`;
            }

            // Next button - always show but disable if needed
            const nextDisabled = currentPage === totalPages - 1;
            html += `<button class="btn btn-small ${nextDisabled ? 'disabled' : ''}"
                     onclick="${nextDisabled ? '' : `changePage(${currentPage + 1})`}">Suivant ›</button>`;

            html += '</div>';
            return html;
        }

        function changePage(newPage) {
            window.currentPage = newPage;
            refreshBuildList();
        }

        function toggleBuildView() {
            window.detailedView = !window.detailedView;
            const toggleBtn = document.getElementById('toggleViewBtn');
            toggleBtn.textContent = window.detailedView ? '📋 Vue compacte' : '📋 Vue détaillée';
            refreshBuildList();
        }

        function filterSuivlines() {
            const searchTerm = document.getElementById('suivlineSearch').value.toLowerCase();
            const allSuivlines = getSavedBuilds().sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

            if (!searchTerm) {
                window.currentSuivlines = allSuivlines;
            } else {
                window.currentSuivlines = allSuivlines.filter(suivline => {
                    // Search in name
                    if (suivline.name.toLowerCase().includes(searchTerm)) return true;

                    // Search in stats
                    const allStats = [];
                    suivline.selectedStats.forEach((tierStats, tierIndex) => {
                        const extracted = extractStatsFromTier(tierStats, tierIndex);
                        allStats.push(...extracted);
                    });

                    return allStats.some(stat => stat.toLowerCase().includes(searchTerm));
                });
            }

            window.currentPage = 0;
            refreshBuildList();

            // Update results count
            const resultsCount = document.getElementById('resultsCount');
            resultsCount.textContent = `${window.currentSuivlines.length} Suivlines`;
        }

        function refreshBuildList() {
            const buildList = document.getElementById('buildList');
            const paginationControls = document.getElementById('paginationControls');

            // Store current selections before refreshing
            const selectedCheckboxes = Array.from(document.querySelectorAll('#compareModal input[type="checkbox"]:checked')).map(cb => cb.value);

            buildList.innerHTML = createBuildListHTML(window.currentSuivlines, window.currentPage * window.itemsPerPage, window.itemsPerPage);
            paginationControls.innerHTML = createPaginationHTML(window.currentSuivlines.length, window.currentPage, window.itemsPerPage);

            // No need to store allSuivlines separately since we get it fresh in createBuildListHTML

            // Restore selections
            selectedCheckboxes.forEach(value => {
                const checkbox = document.querySelector(`#compareModal input[value="${value}"]`);
                if (checkbox) {
                    checkbox.checked = true;
                }
            });

            // Update selection state
            updateCompareSelection();
        }

        function toggleBuildDetails(buildIndex, event) {
            if (event) event.stopPropagation();

            const buildItem = document.querySelector(`[data-index="${buildIndex}"]`);
            if (!buildItem) return;

            // Find the suivline - buildIndex is the global index in the original saved builds array
            const allSuivlines = getSavedBuilds();
            const suivline = allSuivlines[buildIndex];
            if (!suivline) return;

            // Store current selections before refreshing
            const selectedCheckboxes = Array.from(document.querySelectorAll('#compareModal input[type="checkbox"]:checked')).map(cb => cb.value);

            if (buildItem.classList.contains('compact')) {
                // Switch to detailed view for this item
                buildItem.outerHTML = createDetailedBuildItem(suivline, buildIndex);
            } else {
                // Switch back to compact view
                buildItem.outerHTML = createCompactBuildItem(suivline, buildIndex);
            }

            // Restore selections after DOM update
            selectedCheckboxes.forEach(value => {
                const checkbox = document.querySelector(`#compareModal input[value="${value}"]`);
                if (checkbox) {
                    checkbox.checked = true;
                }
            });

            // Update selection state
            updateCompareSelection();
        }

        function hideBuildSelector() {
            const buildSelector = document.querySelector('.build-selector');
            const comparisonResults = document.getElementById('comparisonResults');

            buildSelector.style.display = 'none';

            // Add show button to comparison results
            const showButton = `
                <div class="show-builds-section">
                    <button class="btn btn-secondary" onclick="showBuildSelector()">
                        📋 Comparer autre build
                    </button>
                </div>
            `;

            comparisonResults.insertAdjacentHTML('afterbegin', showButton);
        }

        function showBuildSelector() {
            const buildSelector = document.querySelector('.build-selector');
            const showBuildsSection = document.querySelector('.show-builds-section');

            buildSelector.style.display = 'block';
            if (showBuildsSection) {
                showBuildsSection.remove();
            }
        }

        function updateCompareSelection() {
            const checkboxes = document.querySelectorAll('#compareModal input[type="checkbox"]:checked');
            const compareBtn = document.getElementById('compareSelectedBtn');
            const selectionInfo = document.getElementById('selectionInfo');

            const selectedItems = Array.from(checkboxes).map(cb => cb.value);

            // Update global selection set
            if (window.selectedItems) {
                window.selectedItems.clear();
                selectedItems.forEach(item => window.selectedItems.add(item));
            }

            const buildSelections = selectedItems.filter(item => item.startsWith('build-'));
            const tierSelections = selectedItems.filter(item => item.startsWith('tier-'));

            const totalSelections = buildSelections.length + tierSelections.length;

            compareBtn.disabled = totalSelections < 2;

            if (totalSelections < 2) {
                compareBtn.textContent = 'Sélectionnez au moins 2 éléments';
                selectionInfo.textContent = 'Sélectionnez au moins 2 éléments pour comparer';
            } else {
                compareBtn.textContent = `Comparer les ${totalSelections} sélectionnés`;

                let infoText = '';
                if (buildSelections.length > 0) {
                    infoText += `${buildSelections.length} Suivline${buildSelections.length > 1 ? 's' : ''}`;
                }
                if (tierSelections.length > 0) {
                    if (infoText) infoText += ' + ';
                    infoText += `${tierSelections.length} tier${tierSelections.length > 1 ? 's' : ''}`;
                }
                selectionInfo.textContent = infoText + ' sélectionnés';
            }
        }

        function compareSelectedBuilds() {
            const checkboxes = document.querySelectorAll('#compareModal input[type="checkbox"]:checked');
            const selectedItems = Array.from(checkboxes).map(cb => cb.value);
            const savedSuivlines = getSavedBuilds();

            const comparisonItems = [];

            selectedItems.forEach(item => {
                if (item.startsWith('build-')) {
                    const buildIndex = parseInt(item.replace('build-', ''));
                    const suivline = savedSuivlines[buildIndex];
                    comparisonItems.push({
                        type: 'build',
                        name: suivline.name,
                        data: suivline,
                        displayName: suivline.name
                    });
                } else if (item.startsWith('tier-')) {
                    const parts = item.replace('tier-', '').split('-');
                    const buildIndex = parseInt(parts[0]);
                    const tierIndex = parseInt(parts[1]);
                    const suivline = savedSuivlines[buildIndex];
                    const tierName = suivline.tierNames[tierIndex];

                    comparisonItems.push({
                        type: 'tier',
                        name: `${suivline.name} - ${tierName}`,
                        data: {
                            ...suivline,
                            selectedStats: suivline.selectedStats.map((tierStats, index) =>
                                index === tierIndex ? tierStats : []
                            )
                        },
                        displayName: `${suivline.name}`,
                        tierName: tierName
                    });
                }
            });

            displayAdvancedComparison(comparisonItems);
            hideBuildSelector();
        }

        function displayAdvancedComparison(comparisonItems) {
            const resultsDiv = document.getElementById('comparisonResults');

            let comparisonHTML = `
                <div class="comparison-header">
                    <h3>📊 Comparaison des Caractéristiques</h3>
                    <div class="comparison-summary">
                        Comparaison de ${comparisonItems.length} éléments
                    </div>
                </div>
                <div class="advanced-comparison">
                    ${comparisonItems.map(item => createAdvancedComparisonCard(item)).join('')}
                </div>
            `;

            resultsDiv.innerHTML = comparisonHTML;
        }

        function createAdvancedComparisonCard(item) {
            const date = new Date(item.data.timestamp).toLocaleDateString();
            const statsCount = countSuivlineStats(item.data.selectedStats);

            let characteristicsHTML = '';

            item.data.selectedStats.forEach((tierStats, tierIndex) => {
                const tierStatsExtracted = extractStatsFromTier(tierStats, tierIndex);
                if (tierStatsExtracted.length > 0) {
                    const tierName = item.data.tierNames[tierIndex];
                    characteristicsHTML += `
                        <div class="tier-characteristics">
                            <div class="tier-char-header">${tierName}</div>
                            <div class="tier-char-stats">
                                ${tierStatsExtracted.map(stat => {
                                    const iconUrl = getStatIcon(stat);
                                    const iconHtml = iconUrl ? `<img src="${iconUrl}" alt="${stat}" class="char-stat-icon" onerror="this.style.display='none'">` : '';
                                    return `
                                        <div class="char-stat-item">
                                            ${iconHtml}
                                            <span class="char-stat-name">${stat}</span>
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                        </div>
                    `;
                }
            });

            if (!characteristicsHTML) {
                characteristicsHTML = '<div class="no-characteristics">Aucune caractéristique</div>';
            }

            const typeIcon = item.type === 'build' ? '⚔️' : '🎯';
            const typeLabel = item.type === 'build' ? 'Suivline complète' : `Tier: ${item.tierName}`;

            return `
                <div class="advanced-comparison-card ${item.type}">
                    <div class="comparison-card-header">
                        <div class="comparison-card-icon">${typeIcon}</div>
                        <div class="comparison-card-info">
                            <div class="comparison-card-name">${item.displayName}</div>
                            <div class="comparison-card-type">${typeLabel}</div>
                            <div class="comparison-card-meta">
                                <span class="comparison-card-date">📅 ${date}</span>
                                <span class="comparison-card-stats">⚔️ ${statsCount} stats</span>
                            </div>
                        </div>
                    </div>
                    <div class="comparison-card-characteristics">
                        ${characteristicsHTML}
                    </div>
                </div>
            `;
        }



        function closeCompareModal() {
            const modal = document.getElementById('compareModal');
            if (modal) {
                modal.remove();
            }
        }
    </script>
</body>
</html>
