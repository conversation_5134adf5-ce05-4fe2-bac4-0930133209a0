
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --background-color: #f8f9fa;
            --background-color2: #ffffff;
            --card-background: #ffffff;
            --text-color: #2c3e50;
            --border-color: #dee2e6;
            --tier-color: #5c4e00;
            --option-text-color: #495057;
            --main-text-color: #6c757d;
            --highlight-color: #f8fbff;
            --highlight-border: #575a5e80;
            --highlight-shadow: rgba(190, 203, 214, 0.37);
            --shadow: 0 4px 6px rgba(0,0,0,0.07);
            --shadow-hover: 0 8px 25px rgba(0,0,0,0.15);
        }

        [data-theme="dark"] {
            --primary-color: #34495e;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --background-color: #182936;
            --background-color2: #182936;
            --card-background: #2d3748;
            --text-color: #e2e8f0;
            --border-color: #4a5568;
            --tier-color: #ffee00;
            --option-text-color: #b8c5d1;
            --main-text-color: #8a9ba8;
            --highlight-color: #182b3a;
            --highlight-border: #899199;
            --highlight-shadow: rgba(94, 94, 94, 0.4);
            --shadow: 0 4px 6px rgba(0,0,0,0.3);
            --shadow-hover: 0 8px 25px rgba(0,0,0,0.4);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
            transition: all 0.3s ease;
        }

        .dashboard-container {
            display: grid;
            grid-template-columns: 280px 1fr 280px;
            gap: 12px;
            max-width: 1600px;
            margin: 0 auto;
            padding: 12px;
            min-height: 100vh;
        }

        /* Left Sidebar - Controls & Stats Counter */
        .left-sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
            position: sticky;
            top: 20px;
            height: fit-content;
        }

        .header-card {
            background: var(--card-background);
            padding: 25px;
            border-radius: 12px;
            box-shadow: var(--shadow);
            text-align: center;
        }

        .header-card h1 {
            color: var(--main-text-color);
            margin-bottom: 8px;
            font-size: 1.8em;
            font-weight: 700;
        }

        .header-card p {
            color: var(--text-color);
            opacity: 0.7;
            font-size: 0.9em;
        }

        .controls-card {
            background: var(--card-background);
            padding: 20px;
            border-radius: 12px;
            box-shadow: var(--shadow);
        }

        .controls-title {
            font-size: 1.1em;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--main-text-color);
        }

        .controls-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .btn {
            background: var(--secondary-color);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .btn:hover {
            background: #2980b9;
            transform: translateY(-1px);
            box-shadow: var(--shadow-hover);
        }

        .btn-danger {
            background: var(--accent-color);
            grid-column: 1 / -1;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .search-card {
            background: var(--card-background);
            padding: 20px;
            border-radius: 12px;
            box-shadow: var(--shadow);
        }

        .search-title {
            font-size: 1.1em;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--main-text-color);
        }

        .search-box {
            position: relative;
        }

        #statSearch {
            width: 100%;
            padding: 12px 40px 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 14px;
            background: var(--card-background);
            color: var(--main-text-color);
            transition: border-color 0.3s ease;
        }

        #statSearch:focus {
            outline: none;
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        #statSearch::placeholder {
            color: var(--main-text-color);
            opacity: 0.7;
        }

        .search-clear {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            font-size: 16px;
            cursor: pointer;
            color: var(--text-color);
            opacity: 0.6;
            transition: opacity 0.3s ease;
        }

        .search-clear:hover {
            opacity: 1;
        }

        .tier-select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 14px;
            background: var(--card-background);
            color: var(--text-color);
            transition: border-color 0.3s ease;
            cursor: pointer;
        }

        .tier-select:focus {
            outline: none;
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .tier-container {
            margin-bottom: 25px;
            background: var(--background-color);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid var(--border-color);
        }

        .tier-container h3 {
            color: var(--option-text-color);
            margin-bottom: 15px;
            font-size: 1.1em;
            font-weight: 600;
            padding-bottom: 8px;
            border-bottom: 2px solid #a1a1a733;
        }

        .stat-group {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 8px;
        }



        /* Center Content - Tiers */
        .center-content {
            display: flex;
            flex-direction: column;
            gap: 12px;
            overflow-y: auto;
            max-height: calc(100vh - 30px);
            min-height: 0; /* Allow flex shrinking */
            padding-top: 10px;
        }

        .tier-section {
            background: var(--card-background);
            border-radius: 12px;
            box-shadow: var(--shadow);
            overflow: visible; /* Changed from hidden to visible */
            flex-shrink: 0; /* Prevent shrinking */
            transition: all 0.3s ease;
        }

        .tier-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            user-select: none;
        }

        .tier-header:hover {
            background: linear-gradient(135deg, #34495e, #2980b9);
            transform: translateY(-1px);
        }

        .tier-header.active {
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
        }

        .tier-title {
            font-size: 1.2em;
            font-weight: 600;
        }

        .tier-limit {
            background: rgba(255, 255, 255, 0.2);
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 0.85em;
        }

        .tier-toggle {
            font-size: 1.2em;
            transition: transform 0.3s ease;
        }

        .tier-toggle.rotated {
            transform: rotate(180deg);
        }

        .tier-content {
            padding: 12px;
            display: none;
            animation: slideDown 0.3s ease;
            max-height: none; /* Remove height restrictions */
            overflow: visible; /* Allow content to be fully visible */
        }

        .tier-content.show {
            display: block;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
            gap: 8px;
        }

        .stat-button {
            background: var(--card-background);
            border: 2px solid var(--border-color);
            padding: 8px 10px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            color: var(--text-color);
            text-align: left;
        }

        .stat-button:hover {
            border-color: var(--secondary-color);
            background: rgba(52, 152, 219, 0.05);
            transform: translateY(-1px);
            box-shadow: var(--shadow-hover);
        }

        .stat-button.selected {
            background: var(--secondary-color);
            color: white;
            border-color: var(--secondary-color);
            box-shadow: var(--shadow-hover);
        }

        .stat-button.selected:hover {
            background: #2980b9;
        }

        .stat-icon {
            width: 20px;
            height: 20px;
            object-fit: contain;
            flex-shrink: 0;
        }

        /* Right Sidebar - Selected Stats */
        .right-sidebar {
            display: flex;
            flex-direction: column;
            gap: 12px;
            position: sticky;
            top: 20px;
            height: fit-content;
            min-width: 280px; /* Ensure minimum width */
            max-width: 320px; /* Prevent it from getting too wide */
        }

        .selected-stats {
            background: var(--card-background);
            padding: 16px;
            border-radius: 8px;
            box-shadow: var(--shadow);
            max-height: calc(100vh - 200px);
            overflow-y: auto;
            /* border: 2px solid #444; */
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .selected-stats h3 {
            color: var(--tier-color);
            margin-bottom: 12px;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            border-bottom: 1px solid #8888884d;
            padding-bottom: 8px;
        }

        .selected-list {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .selected-item {
            background: transparent;
            padding: 4px 8px;
            border-radius: 0;
            display: flex;
            align-items: center;
            gap: 8px;
            border: none;
            transition: all 0.2s ease;
            font-size: 13px;
            line-height: 1.3;
        }

        .selected-item:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .selected-info {
            display: flex;
            align-items: center;
            gap: 6px;
            flex: 1;
        }

        .selected-item .stat-icon {
            width: 16px;
            height: 16px;
            flex-shrink: 0;
        }

        .selected-item .stat-text {
            color: #ffffff;
            flex: 1;
            font-weight: 500;
        }

        .selected-tier {
            font-size: 10px;
            background: #8b4513;
            color: #f4d03f;
            padding: 1px 4px;
            border-radius: 2px;
            margin-left: 4px;
            font-weight: bold;
        }

        .remove-btn {
            background: #8b4513;
            color: white;
            border: none;
            width: 16px;
            height: 16px;
            border-radius: 2px;
            cursor: pointer;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            opacity: 0.7;
        }

        .remove-btn:hover {
            background: #a0522d;
            opacity: 1;
        }

        .tier-stats-group {
            margin-bottom: 12px;
            border: none;
            border-radius: 0;
            overflow: visible;
        }

        .tier-stats-header {
            background: transparent;
            color: var(--tier-color);
            padding: 6px 0;
            font-weight: bold;
            font-size: 12px;
            text-transform: uppercase;
            border-bottom: 1px solid #8888884d;
            margin-bottom: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .tier-stats-header:hover {
            color: var(--secondary-color);
            background: rgba(255, 255, 255, 0.05);
            padding-left: 4px;
        }

        .tier-stats-content {
            padding: 0;
        }

        .tier-stats-content .selected-item {
            margin-bottom: 8px;
        }

        .tier-stats-content .selected-item:last-child {
            margin-bottom: 0;
        }

        .placeholder-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 4px 8px;
            background: transparent;
            border: 1px dashed #555;
            border-radius: 0;
            margin-bottom: 2px;
            opacity: 0.5;
            transition: all 0.2s ease;
            font-size: 13px;
            cursor: pointer;
        }

        .placeholder-item:hover {
            opacity: 0.8;
            border-color: var(--secondary-color);
            background: rgba(255, 255, 255, 0.05);
        }

        .placeholder-info {
            display: flex;
            align-items: center;
            gap: 6px;
            flex: 1;
        }

        .placeholder-icon {
            font-size: 12px;
            opacity: 0.6;
        }

        .placeholder-text {
            color: var(--main-text-color);
            font-style: italic;
            font-size: 12px;
        }

        /* Option highlighting styles */
        .option-group.highlighted {
            background: var(--highlight-color);
            border: 2px solid var(--highlight-border);
            box-shadow: 0 0 25px var(--highlight-shadow), 0 0 50px var(--highlight-shadow);
            transform: scale(1.001);
            z-index: 10;
            position: relative;
        }

        .option-group {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .tier-content.highlighting .option-group:not(.highlighted) {
            opacity: 0.3;
            filter: blur(2px);
            transform: scale(0.96);
        }

        /* Add a subtle pulse animation to the highlighted option */
        .option-group.highlighted {
            animation: highlightPulse 2s ease-in-out infinite;
        }

        /* @keyframes highlightPulse {
            0%, 100% {
                box-shadow: 0 0 25px rgba(255, 238, 0, 0.4), 0 0 50px rgba(255, 238, 0, 0.2);
            }
            50% {
                box-shadow: 0 0 30px rgba(255, 238, 0, 0.6), 0 0 60px rgba(255, 238, 0, 0.3);
            }
        } */

        .highlight {
            background-color: #8b7324;
            color: #041185;
            padding: 2px 4px;
            border-radius: 3px;
        }

        [data-theme="dark"] .highlight {
            background-color: #f39c12;
            color: white;
        }

        /* Comparison Modal Styles */
        #compareModal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
        }

        .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: var(--card-background);
            border-radius: 12px;
            max-width: 90vw;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            border: 1px solid var(--border-color);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .modal-header h2 {
            margin: 0;
            color: var(--text-color);
            font-size: 1.5em;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: var(--text-color);
            padding: 5px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .modal-close:hover {
            background: var(--border-color);
        }

        .modal-body {
            padding: 20px;
            min-width: 600px;
        }

        .build-selector h3 {
            color: var(--option-text-color);
            margin-bottom: 15px;
        }

        .build-list {
            display: grid;
            gap: 10px;
            margin-bottom: 20px;
            max-height: 300px;
            overflow-y: auto;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
        }

        .build-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            background: var(--background-color2);
        }

        .build-item:hover {
            background: var(--highlight-color);
            border-color: var(--highlight-border);
        }

        .build-item input[type="checkbox"] {
            margin: 0;
        }

        .build-info {
            flex: 1;
        }

        .build-name {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 4px;
        }

        .build-date {
            font-size: 0.9em;
            color: var(--search-text-color);
        }

        .compare-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .comparison-results {
            margin-top: 20px;
        }

        .comparison-results h3 {
            color: var(--option-text-color);
            margin-bottom: 15px;
        }

        .comparison-table {
            overflow-x: auto;
        }

        .comparison-table table {
            width: 100%;
            border-collapse: collapse;
            background: var(--background-color2);
            border-radius: 8px;
            overflow: hidden;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .comparison-table th {
            background: var(--background-color);
            font-weight: 600;
            color: var(--text-color);
        }

        .stat-name {
            font-weight: 500;
            color: var(--option-text-color);
        }

        .stat-value {
            text-align: center;
            font-weight: 600;
        }

        .has-stat {
            color: var(--success-color);
        }

        .no-stat {
            color: var(--search-text-color);
            opacity: 0.6;
        }

        /* Save Modal Styles */
        .save-modal {
            min-width: 500px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--option-text-color);
        }

        .form-group input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 14px;
            background: var(--background-color2);
            color: var(--text-color);
            transition: border-color 0.3s ease;
        }

        .form-group input[type="text"]:focus {
            outline: none;
            border-color: var(--secondary-color);
        }

        .tier-selection {
            display: grid;
            gap: 8px;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--background-color2);
        }

        .tier-option {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .tier-option:hover:not(.disabled) {
            background: var(--highlight-color);
        }

        .tier-option.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .tier-option input[type="radio"] {
            margin: 0;
        }

        .save-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        /* Notification Styles */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 2000;
            padding: 16px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 400px;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: var(--success-color);
            color: white;
        }

        .notification.error {
            background: var(--accent-color);
            color: white;
        }

        .notification.info {
            background: var(--secondary-color);
            color: white;
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .notification-icon {
            font-weight: bold;
            font-size: 16px;
        }

        /* Suivlines Page Styles */
        .suivlines-page {
            min-width: 800px;
            max-width: 1200px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--search-text-color);
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: var(--option-text-color);
        }

        .suivlines-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            padding: 10px;
        }

        /* Dofus-style Suivline Cards */
        .suivline-card {
            background: linear-gradient(145deg, #2a2a2a 0%, #1e1e1e 100%);
            border: 2px solid #4a4a4a;
            border-radius: 12px;
            padding: 16px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        [data-theme="light"] .suivline-card {
            background: linear-gradient(145deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #dee2e6;
        }

        .suivline-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            border-color: #ffee00;
        }

        .suivline-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #ffee00, #ffd700, #ffee00);
        }

        .suivline-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid rgba(255, 238, 0, 0.2);
        }

        .suivline-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(145deg, #ffee00, #ffd700);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            box-shadow: 0 2px 8px rgba(255, 238, 0, 0.3);
        }

        .suivline-info {
            flex: 1;
        }

        .suivline-name {
            font-size: 16px;
            font-weight: 700;
            color: #ffee00;
            margin-bottom: 4px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }

        [data-theme="light"] .suivline-name {
            color: #5c4e00;
            text-shadow: none;
        }

        .suivline-meta {
            display: flex;
            gap: 12px;
            font-size: 12px;
            color: #b8b8b8;
        }

        [data-theme="light"] .suivline-meta {
            color: #6c757d;
        }

        .suivline-date::before {
            content: '📅 ';
        }

        .suivline-stats::before {
            content: '⚔️ ';
        }

        .suivline-preview {
            margin-bottom: 16px;
            min-height: 80px;
        }

        .tier-preview {
            margin-bottom: 8px;
            padding: 8px;
            background: rgba(255, 238, 0, 0.1);
            border-radius: 6px;
            border-left: 3px solid #ffee00;
        }

        .tier-preview-name {
            font-size: 12px;
            font-weight: 600;
            color: #ffee00;
            margin-bottom: 4px;
            text-transform: uppercase;
        }

        [data-theme="light"] .tier-preview-name {
            color: #5c4e00;
        }

        .tier-preview-stats {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
        }

        .stat-preview {
            background: rgba(255, 255, 255, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            color: #e0e0e0;
            border: 1px solid rgba(255, 238, 0, 0.3);
        }

        [data-theme="light"] .stat-preview {
            background: rgba(0, 0, 0, 0.05);
            color: #495057;
            border: 1px solid rgba(92, 78, 0, 0.3);
        }

        .stat-more {
            background: rgba(255, 238, 0, 0.2);
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            color: #ffee00;
            font-weight: 600;
        }

        [data-theme="light"] .stat-more {
            background: rgba(92, 78, 0, 0.2);
            color: #5c4e00;
        }

        .no-stats {
            color: #888;
            font-style: italic;
            text-align: center;
            padding: 20px;
        }

        .suivline-actions {
            display: flex;
            gap: 8px;
            justify-content: space-between;
        }

        .suivline-actions .btn {
            flex: 1;
            padding: 8px 12px;
            font-size: 12px;
            border-radius: 6px;
        }

        /* Confirm Modal */
        .confirm-modal {
            min-width: 400px;
        }

        .warning-text {
            color: var(--accent-color);
            font-style: italic;
            margin-top: 10px;
        }

        .confirm-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        /* Comparison Modal Styles */
        .comparison-modal {
            min-width: 900px;
            max-width: 95vw;
        }

        .build-stats {
            font-size: 0.85em;
            color: var(--search-text-color);
            margin-top: 2px;
        }

        .suivlines-comparison {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .suivline-comparison-card {
            background: linear-gradient(145deg, #2a2a2a 0%, #1e1e1e 100%);
            border: 2px solid #4a4a4a;
            border-radius: 12px;
            padding: 16px;
            position: relative;
            overflow: hidden;
        }

        [data-theme="light"] .suivline-comparison-card {
            background: linear-gradient(145deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #dee2e6;
        }

        .suivline-comparison-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #ffee00, #ffd700, #ffee00);
        }

        .comparison-card-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid rgba(255, 238, 0, 0.2);
        }

        .comparison-card-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(145deg, #ffee00, #ffd700);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            box-shadow: 0 2px 6px rgba(255, 238, 0, 0.3);
        }

        .comparison-card-info {
            flex: 1;
        }

        .comparison-card-name {
            font-size: 14px;
            font-weight: 700;
            color: #ffee00;
            margin-bottom: 4px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }

        [data-theme="light"] .comparison-card-name {
            color: #5c4e00;
            text-shadow: none;
        }

        .comparison-card-meta {
            display: flex;
            gap: 10px;
            font-size: 11px;
            color: #b8b8b8;
        }

        [data-theme="light"] .comparison-card-meta {
            color: #6c757d;
        }

        .tier-characteristics {
            margin-bottom: 12px;
            padding: 8px;
            background: rgba(255, 238, 0, 0.1);
            border-radius: 6px;
            border-left: 3px solid #ffee00;
        }

        .tier-char-header {
            font-size: 11px;
            font-weight: 600;
            color: #ffee00;
            margin-bottom: 6px;
            text-transform: uppercase;
        }

        [data-theme="light"] .tier-char-header {
            color: #5c4e00;
        }

        .tier-char-stats {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .char-stat-item {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 3px 6px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
            border: 1px solid rgba(255, 238, 0, 0.2);
        }

        [data-theme="light"] .char-stat-item {
            background: rgba(0, 0, 0, 0.03);
            border: 1px solid rgba(92, 78, 0, 0.2);
        }

        .char-stat-icon {
            width: 16px;
            height: 16px;
            flex-shrink: 0;
        }

        .char-stat-name {
            font-size: 11px;
            color: #e0e0e0;
            font-weight: 500;
        }

        [data-theme="light"] .char-stat-name {
            color: #495057;
        }

        .no-characteristics {
            color: #888;
            font-style: italic;
            text-align: center;
            padding: 20px;
            font-size: 12px;
        }

        /* Enhanced Comparison Modal Styles */
        .comparison-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: var(--background-color2);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .search-section {
            flex: 1;
            max-width: 300px;
        }

        #suivlineSearch {
            width: 100%;
            padding: 10px 12px;
            border: 2px solid var(--border-color);
            border-radius: 6px;
            background: var(--background-color);
            color: var(--text-color);
            font-size: 14px;
        }

        #suivlineSearch:focus {
            outline: none;
            border-color: var(--secondary-color);
        }

        .view-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .results-count {
            color: var(--search-text-color);
            font-size: 14px;
        }

        /* Build List Styles */
        .build-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 10px;
            background: var(--background-color2);
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .build-item {
            display: flex;
            align-items: flex-start;
            gap: 8px;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--background-color);
            transition: all 0.2s;
            margin-bottom: 0;
        }

        .build-item:hover {
            background: var(--highlight-color);
            border-color: var(--highlight-border);
        }

        .build-item.clickable {
            cursor: pointer;
        }

        .build-item.clickable:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        [data-theme="light"] .build-item.clickable:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .build-item.detailed {
            flex-direction: column;
            align-items: stretch;
            grid-column: 1 / -1; /* Span full width */
        }

        .build-checkbox {
            margin-top: 2px;
        }

        .build-preview {
            flex: 1;
        }

        .build-header {
            margin-bottom: 8px;
        }

        .build-name {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 3px;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 13px;
        }

        .recent-badge {
            background: var(--success-color);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 500;
        }

        .build-meta {
            display: flex;
            gap: 8px;
            font-size: 11px;
            color: var(--search-text-color);
        }

        .build-quick-preview {
            margin-top: 8px;
        }

        .quick-preview-items {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }

        .compact-preview-items {
            font-size: 11px;
            color: var(--option-text-color);
            line-height: 1.3;
        }

        .preview-item {
            background: rgba(255, 238, 0, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            color: var(--option-text-color);
            border: 1px solid rgba(255, 238, 0, 0.3);
        }

        .preview-more {
            background: rgba(255, 238, 0, 0.2);
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            color: #ffee00;
            font-weight: 600;
        }

        [data-theme="light"] .preview-more {
            color: #5c4e00;
        }

        .no-preview {
            color: var(--search-text-color);
            font-style: italic;
            font-size: 11px;
        }

        .build-actions {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        /* Detailed View Styles */
        .build-header-detailed {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }

        .build-info-detailed {
            flex: 1;
        }

        .tier-selection-grid {
            display: grid;
            gap: 8px;
        }

        .tier-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 4px;
        }

        .tier-grid-header {
            font-size: 11px;
            font-weight: 600;
            color: var(--option-text-color);
            margin-bottom: 6px;
            padding-bottom: 4px;
            border-bottom: 1px solid var(--border-color);
        }

        .tier-selection-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 8px;
            background: var(--background-color2);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            transition: all 0.2s;
            font-size: 11px;
        }

        .tier-selection-item:hover:not(.disabled) {
            background: var(--highlight-color);
            border-color: var(--highlight-border);
        }

        .tier-selection-item.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .tier-info {
            flex: 1;
        }

        .tier-name {
            font-weight: 600;
            color: var(--text-color);
            font-size: 11px;
            margin-bottom: 2px;
        }

        .tier-stats-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            font-size: 10px;
        }

        .tier-stat-item {
            background: rgba(255, 238, 0, 0.1);
            padding: 1px 4px;
            border-radius: 3px;
            font-size: 10px;
            color: var(--option-text-color);
            border: 1px solid rgba(255, 238, 0, 0.3);
        }

        .tier-stat-more {
            background: rgba(255, 238, 0, 0.2);
            padding: 1px 4px;
            border-radius: 3px;
            font-size: 10px;
            color: #ffee00;
            font-weight: 600;
        }

        [data-theme="light"] .tier-stat-more {
            color: #5c4e00;
        }

        .no-tier-stats {
            color: var(--search-text-color);
            font-style: italic;
            font-size: 10px;
        }

        /* Pagination Styles */
        .pagination-controls {
            margin: 15px 0;
            text-align: center;
        }

        .pagination {
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .pagination .btn {
            min-width: 35px;
            height: 35px;
            padding: 6px 10px;
            font-size: 12px;
        }

        .pagination .btn.active {
            background: var(--secondary-color);
            color: white;
            border-color: var(--secondary-color);
        }

        .pagination .btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }

        .btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }

        .pagination-dots {
            color: var(--search-text-color);
            padding: 0 5px;
        }

        /* Compare Actions */
        .compare-actions {
            text-align: center;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid var(--border-color);
        }

        .selection-info {
            margin-top: 8px;
            font-size: 12px;
            color: var(--search-text-color);
        }

        /* Advanced Comparison Results */
        .comparison-header {
            margin-bottom: 20px;
            text-align: center;
        }

        .comparison-summary {
            color: var(--search-text-color);
            font-size: 14px;
            margin-top: 5px;
        }

        .advanced-comparison {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .advanced-comparison-card {
            background: linear-gradient(145deg, #2a2a2a 0%, #1e1e1e 100%);
            border: 2px solid #4a4a4a;
            border-radius: 12px;
            padding: 16px;
            position: relative;
            overflow: hidden;
        }

        [data-theme="light"] .advanced-comparison-card {
            background: linear-gradient(145deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #dee2e6;
        }

        .advanced-comparison-card.tier {
            border-color: #ff6b35;
        }

        .advanced-comparison-card.tier::before {
            background: linear-gradient(90deg, #ff6b35, #ff8c42, #ff6b35);
        }

        .advanced-comparison-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #ffee00, #ffd700, #ffee00);
        }

        .comparison-card-type {
            font-size: 11px;
            color: var(--search-text-color);
            font-style: italic;
            margin-bottom: 4px;
        }

        .advanced-comparison-card.tier .comparison-card-type {
            color: #ff6b35;
            font-weight: 600;
        }

        [data-theme="light"] .advanced-comparison-card.tier .comparison-card-type {
            color: #d63384;
        }

        /* Show Builds Section */
        .show-builds-section {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: var(--background-color2);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .hidden {
            display: none !important;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .dashboard-container {
                grid-template-columns: 280px 1fr 280px;
            }
        }

        @media (max-width: 1024px) {
            .dashboard-container {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto 1fr;
            }

            .left-sidebar {
                flex-direction: row;
                gap: 15px;
                position: static;
            }

            .left-sidebar > * {
                flex: 1;
            }

            .right-sidebar {
                position: static;
            }

            .controls-grid {
                grid-template-columns: repeat(4, 1fr);
            }

            .counter-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        @media (max-width: 768px) {
            .dashboard-container {
                padding: 15px;
                gap: 15px;
            }

            .left-sidebar {
                flex-direction: column;
            }

            .controls-grid {
                grid-template-columns: 1fr 1fr;
            }

            .counter-grid {
                grid-template-columns: 1fr 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }

/* Global Header Styles */
.global-header {
    background: var(--card-background);
    border-bottom: 2px solid var(--border-color);
    padding: 15px 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: var(--shadow);
}

.header-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
}

.header-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 20px;
    font-weight: 600;
    color: var(--text-color);
    text-decoration: none;
}

.header-logo:hover {
    color: var(--secondary-color);
}

.header-nav {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-nav-item {
    padding: 8px 16px;
    border-radius: 8px;
    text-decoration: none;
    color: var(--main-text-color);
    font-weight: 500;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.header-nav-item:hover {
    background: var(--highlight-color);
    color: var(--text-color);
    border-color: var(--border-color);
}

.header-nav-item.active {
    background: var(--secondary-color);
    color: white;
    border-color: var(--secondary-color);
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.header-dropdown {
    position: relative;
    display: inline-block;
}

.header-dropdown-btn {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.header-dropdown-btn:hover {
    background: var(--highlight-color);
    border-color: var(--secondary-color);
}

.header-dropdown-content {
    display: none;
    position: absolute;
    right: 0;
    background: var(--card-background);
    min-width: 120px;
    box-shadow: var(--shadow-hover);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    z-index: 1001;
    margin-top: 5px;
}

.header-dropdown:hover .header-dropdown-content {
    display: block;
}

.header-dropdown-item {
    color: var(--text-color);
    padding: 10px 15px;
    text-decoration: none;
    display: block;
    transition: background 0.3s ease;
    cursor: pointer;
}

.header-dropdown-item:hover {
    background: var(--highlight-color);
}

.header-dropdown-item.active {
    background: var(--secondary-color);
    color: white;
}

/* Mobile header responsiveness */
@media (max-width: 768px) {
    .header-container {
        padding: 0 15px;
        flex-wrap: wrap;
        gap: 10px;
    }

    .header-nav {
        gap: 10px;
    }

    .header-nav-item {
        padding: 6px 12px;
        font-size: 14px;
    }

    .header-controls {
        gap: 8px;
    }
}

/* Mes Suivlines Page Styles */
.main-container {
    min-height: 100vh;
    background: var(--background-color);
    color: var(--text-color);
}

.mes-suivlines-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    width: 100%;
}

.mes-suivlines-header {
    background: var(--card-background);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.mes-suivlines-header h1 {
    color: var(--text-color);
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.mes-suivlines-header p {
    color: var(--main-text-color);
    margin-top: 5px;
}

.mes-suivlines-comparison-section {
    background: var(--card-background);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.mes-suivlines-comparison-section h3 {
    color: var(--text-color);
    margin-bottom: 15px;
}

/* Search section with results counter */
.search-section {
    background: var(--card-background);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.search-section input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--background-color);
    color: var(--text-color);
    font-size: 16px;
}

.search-section input:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(var(--secondary-color-rgb), 0.1);
}

.results-count {
    color: var(--main-text-color);
    font-size: 14px;
    font-weight: 500;
    padding: 8px 12px;
    background: var(--highlight-color);
    border-radius: 6px;
    border: 1px solid var(--border-color);
    text-align: center;
}

/* Builds container with responsive grid */
.builds-container {
    background: var(--card-background);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    min-height: 400px;
}

.builds-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    width: 100%;
}

/* Suivline comparison card styling for mes-suivlines */
.suivline-comparison-card {
    background: linear-gradient(145deg, #2a2a2a 0%, #1e1e1e 100%);
    border: 2px solid #4a4a4a;
    border-radius: 12px;
    padding: 16px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

[data-theme="light"] .suivline-comparison-card {
    background: linear-gradient(145deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #dee2e6;
}

.suivline-comparison-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #ffee00, #ffd700, #ffee00);
}

.suivline-comparison-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border-color: #ffee00;
}

.suivline-comparison-card.selected {
    border-color: #ffee00;
    box-shadow: 0 0 20px rgba(255, 238, 0, 0.4);
}

.suivline-comparison-card.selected::before {
    height: 4px;
    background: linear-gradient(90deg, #ffee00, #ffd700, #ffee00);
}

/* Card selection overlay */
.card-selection-overlay {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 8px;
    padding: 8px 12px;
    opacity: 0;
    transition: all 0.3s ease;
}

[data-theme="light"] .card-selection-overlay {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #dee2e6;
}

.suivline-comparison-card:hover .card-selection-overlay,
.suivline-comparison-card.selected .card-selection-overlay {
    opacity: 1;
}

.selection-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #ffee00;
    font-weight: 600;
}

[data-theme="light"] .selection-indicator {
    color: #5c4e00;
}

.selection-indicator input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #ffee00;
}

/* Tier characteristics with selection */
.tier-characteristics {
    margin-bottom: 12px;
    padding: 8px;
    background: rgba(255, 238, 0, 0.1);
    border-radius: 6px;
    border-left: 3px solid #ffee00;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tier-characteristics:hover {
    background: rgba(255, 238, 0, 0.15);
    transform: translateX(2px);
}

.tier-characteristics.selected {
    background: rgba(255, 238, 0, 0.2);
    border-left-width: 4px;
    box-shadow: 0 2px 8px rgba(255, 238, 0, 0.3);
}

.tier-selection-indicator {
    position: absolute;
    top: 6px;
    right: 6px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.tier-characteristics:hover .tier-selection-indicator,
.tier-characteristics.selected .tier-selection-indicator {
    opacity: 1;
}

.tier-selection-indicator input[type="checkbox"] {
    width: 14px;
    height: 14px;
    accent-color: #ffee00;
}

/* Card actions */
.card-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid rgba(255, 238, 0, 0.2);
}

.card-actions .btn {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.card-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Tier characteristics stats display */
.tier-char-stats {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.char-stat-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    font-size: 12px;
    color: #ffffff;
    border: 1px solid rgba(255, 238, 0, 0.3);
}

[data-theme="light"] .char-stat-item {
    background: rgba(255, 255, 255, 0.8);
    color: #333333;
    border-color: rgba(92, 78, 0, 0.3);
}

.char-stat-name {
    font-weight: 500;
}

/* Recent badge styling */
.recent-badge {
    background: linear-gradient(45deg, #ff6b35, #ff8c42);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
    margin-left: 8px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Pagination section */
.pagination-section {
    background: var(--card-background);
    border-radius: 15px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.pagination-controls {
    display: flex;
    justify-content: center;
    gap: 8px;
}

/* Responsive grid adjustments */
@media (max-width: 1400px) {
    .builds-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 15px;
    }
}

@media (max-width: 1200px) {
    .builds-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
    }
}

@media (max-width: 768px) {
    .builds-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 10px;
    }

    .builds-container {
        padding: 15px;
    }
}

@media (max-width: 480px) {
    .builds-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }
}

/* Visual stats display like in comparer */
.stats-visual-display {
    padding: 15px;
    background: var(--background-color);
    border-top: 1px solid var(--border-color);
}

.tier-char-list {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.tier-char-item {
    background: var(--card-background);
    color: var(--option-text-color);
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 11px;
    border: 1px solid var(--border-color);
}

/* Floating comparison panel */
.floating-comparison {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: var(--card-background);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 15px;
    box-shadow: var(--shadow-hover);
    z-index: 1000;
    min-width: 280px;
    transform: translateY(100px);
    opacity: 0;
    transition: all 0.3s ease;
}

.floating-comparison.visible {
    transform: translateY(0);
    opacity: 1;
}

.floating-comparison.has-selections {
    border-color: var(--secondary-color);
    background: var(--highlight-color);
}

.floating-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.floating-title {
    font-weight: 600;
    color: var(--text-color);
    font-size: 14px;
}

.floating-close {
    background: none;
    border: none;
    color: var(--main-text-color);
    cursor: pointer;
    font-size: 16px;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.floating-close:hover {
    color: var(--accent-color);
}

.floating-selection-info {
    color: var(--main-text-color);
    font-size: 12px;
    margin-bottom: 10px;
}

.floating-actions {
    display: flex;
    gap: 8px;
}

.floating-actions .btn {
    padding: 8px 12px;
    font-size: 12px;
}

/* Responsive design for mes-suivlines */
@media (max-width: 768px) {
    .mes-suivlines-container {
        padding: 10px;
    }

    .theme-toggle {
        position: relative;
        top: auto;
        right: auto;
        margin-bottom: 20px;
    }

    .floating-comparison {
        bottom: 10px;
        right: 10px;
        min-width: 250px;
    }
}
