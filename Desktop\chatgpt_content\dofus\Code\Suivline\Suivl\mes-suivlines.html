<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mes Suivlines - Dofus Stats</title>
    <link rel="stylesheet" href="style.css">
    <style>
        /* Additional styles specific to mes-suivlines page */
        body {
            background: var(--background-color);
            color: var(--text-color);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: var(--card-background);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
        }

        .header h1 {
            color: var(--text-color);
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .header p {
            color: var(--main-text-color);
            margin-top: 5px;
        }

        .back-btn {
            background: var(--secondary-color);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            margin-right: 15px;
        }

        .back-btn:hover {
            background: var(--primary-color);
            transform: translateY(-2px);
        }

        .comparison-section {
            background: var(--card-background);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
        }

        .comparison-section h3 {
            color: var(--text-color);
            margin-bottom: 15px;
        }

        .comparison-controls {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        .search-section {
            margin-bottom: 20px;
        }

        .search-section input {
            width: 100%;
            max-width: 400px;
            padding: 12px 15px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
            background: var(--background-color);
            color: var(--text-color);
        }

        .search-section input:focus {
            outline: none;
            border-color: var(--secondary-color);
        }

        .suivlines-grid {
            background: var(--card-background);
            border-radius: 15px;
            padding: 20px;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
        }

        .selection-info {
            background: var(--background-color);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            text-align: center;
            color: var(--main-text-color);
        }

        .results-count {
            color: var(--main-text-color);
            font-size: 14px;
            margin-bottom: 15px;
        }

        .pagination-controls {
            margin: 20px 0;
            text-align: center;
        }

        .pagination {
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .pagination .btn {
            min-width: 35px;
            height: 35px;
            padding: 6px 10px;
            font-size: 12px;
        }

        .pagination .btn.active {
            background: var(--primary-color);
        }

        .pagination .btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }

        /* Build item styles matching main theme */
        .build-item {
            background: var(--card-background);
            border: 2px solid var(--border-color);
            border-radius: 12px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
            position: relative;
        }

        .build-item:hover {
            border-color: var(--highlight-border);
            background: var(--highlight-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow-hover);
        }

        .build-item.selected {
            border-color: var(--secondary-color);
            background: var(--highlight-color);
        }

        .comparison-results {
            background: var(--card-background);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
        }

        .comparison-results h3 {
            color: var(--text-color);
            margin-bottom: 15px;
        }

        /* Build item detailed styles */
        .build-header, .build-header-detailed {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            border-bottom: 1px solid var(--border-color);
        }

        .build-checkbox {
            flex-shrink: 0;
        }

        .build-checkbox input[type="checkbox"] {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }

        .build-info, .build-info-detailed {
            flex: 1;
            min-width: 0;
        }

        .build-name {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .recent-badge {
            background: var(--success-color);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 500;
        }

        .build-meta {
            display: flex;
            gap: 15px;
            color: var(--main-text-color);
            font-size: 12px;
        }

        .build-actions {
            display: flex;
            gap: 8px;
            flex-shrink: 0;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
            min-width: auto;
        }

        .compact-preview {
            padding: 15px;
            background: var(--background-color);
            border-top: 1px solid var(--border-color);
        }

        .compact-preview-items {
            color: var(--option-text-color);
            font-size: 13px;
        }

        .no-preview {
            color: var(--main-text-color);
            font-style: italic;
            opacity: 0.7;
        }

        /* Visual stats display like in comparer */
        .stats-visual-display {
            padding: 15px;
            background: var(--background-color);
            border-top: 1px solid var(--border-color);
        }

        .tier-characteristics {
            margin-bottom: 12px;
            padding: 8px;
            background: rgba(255, 238, 0, 0.1);
            border-radius: 6px;
            border-left: 3px solid var(--tier-color);
        }

        [data-theme="dark"] .tier-characteristics {
            background: rgba(255, 238, 0, 0.05);
        }

        .tier-char-header {
            font-size: 11px;
            font-weight: 600;
            color: var(--tier-color);
            margin-bottom: 6px;
            text-transform: uppercase;
        }

        .tier-char-list {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
        }

        .tier-char-item {
            background: var(--card-background);
            color: var(--option-text-color);
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 11px;
            border: 1px solid var(--border-color);
        }

        .tier-selection-grid {
            padding: 15px;
            background: #f7fafc;
        }

        .tier-grid-header {
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 10px;
        }

        .tier-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .tier-selection-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .tier-selection-item.clickable:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .tier-selection-item.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .tier-info {
            flex: 1;
        }

        .tier-name {
            font-weight: 500;
            color: #2d3748;
        }

        .tier-stats-count {
            font-size: 12px;
            color: #718096;
        }

        .clickable {
            cursor: pointer;
        }

        /* Advanced comparison styles matching main file */
        .comparison-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .comparison-card {
            background: linear-gradient(145deg, #2a2a2a 0%, #1e1e1e 100%);
            border: 2px solid #4a4a4a;
            border-radius: 12px;
            padding: 16px;
            position: relative;
            overflow: hidden;
        }

        [data-theme="light"] .comparison-card {
            background: linear-gradient(145deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid var(--border-color);
        }

        .comparison-card.tier {
            border-color: #ff6b35;
        }

        .comparison-card.tier::before {
            background: linear-gradient(90deg, #ff6b35, #ff8c42, #ff6b35);
        }

        .comparison-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--tier-color), #ffd700, var(--tier-color));
        }

        .comparison-card-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid rgba(255, 238, 0, 0.2);
        }

        .comparison-card-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(145deg, var(--tier-color), #ffd700);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            box-shadow: 0 2px 6px rgba(255, 238, 0, 0.3);
        }

        .comparison-card-info {
            flex: 1;
        }

        .comparison-card-name {
            font-size: 14px;
            font-weight: 700;
            color: var(--tier-color);
            margin-bottom: 4px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }

        [data-theme="light"] .comparison-card-name {
            text-shadow: none;
        }

        .comparison-card-type {
            font-size: 11px;
            color: var(--main-text-color);
            font-style: italic;
            margin-bottom: 4px;
        }

        .comparison-card.tier .comparison-card-type {
            color: #ff6b35;
            font-weight: 600;
        }

        [data-theme="light"] .comparison-card.tier .comparison-card-type {
            color: #d63384;
        }

        .comparison-card-meta {
            display: flex;
            gap: 10px;
            font-size: 11px;
            color: var(--main-text-color);
        }

        .comparison-content {
            padding: 0;
        }

        /* Floating comparison panel */
        .floating-comparison {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--card-background);
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 15px;
            box-shadow: var(--shadow-hover);
            z-index: 1000;
            min-width: 280px;
            transform: translateY(100px);
            opacity: 0;
            transition: all 0.3s ease;
        }

        .floating-comparison.visible {
            transform: translateY(0);
            opacity: 1;
        }

        .floating-comparison.has-selections {
            border-color: var(--secondary-color);
            background: var(--highlight-color);
        }

        .floating-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .floating-title {
            font-weight: 600;
            color: var(--text-color);
            font-size: 14px;
        }

        .floating-close {
            background: none;
            border: none;
            color: var(--main-text-color);
            cursor: pointer;
            font-size: 16px;
            padding: 0;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .floating-close:hover {
            color: var(--accent-color);
        }

        .floating-selection-info {
            color: var(--main-text-color);
            font-size: 12px;
            margin-bottom: 10px;
        }

        .floating-actions {
            display: flex;
            gap: 8px;
        }

        .floating-actions .btn {
            padding: 8px 12px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <button class="back-btn" onclick="goBack()">← Retour</button>
            <h1>📁 Mes Suivlines</h1>
            <p>Gérez et comparez vos builds sauvegardées</p>
        </div>

        <div class="comparison-section">
            <h3>🔍 Comparaison de builds</h3>
            <div class="comparison-controls">
                <button class="btn" id="compareSelectedBtn" onclick="compareSelectedBuilds()" disabled>
                    Comparer les sélectionnés
                </button>
                <button class="btn btn-secondary" id="toggleViewBtn" onclick="toggleBuildView()">
                    📋 Vue détaillée
                </button>
                <button class="btn btn-secondary" onclick="clearAllSelections()">
                    🗑️ Effacer sélections
                </button>
            </div>
            <div class="selection-info" id="selectionInfo">
                Sélectionnez au moins 2 éléments pour comparer
            </div>
        </div>

        <div class="search-section">
            <input type="text" id="suivlineSearch" placeholder="🔍 Rechercher une Suivline..." onkeyup="filterSuivlines()">
        </div>

        <div class="suivlines-grid">
            <div class="results-count" id="resultsCount">0 Suivlines</div>
            <div class="build-list" id="buildList">
                <!-- Builds will be loaded here -->
            </div>
            <div class="pagination-controls" id="paginationControls">
                <!-- Pagination will be loaded here -->
            </div>
        </div>

        <div class="comparison-results" id="comparisonResults" style="display: none;">
            <!-- Comparison results will be shown here -->
        </div>

        <!-- Floating comparison panel -->
        <div class="floating-comparison" id="floatingComparison">
            <div class="floating-header">
                <div class="floating-title">🔍 Comparaison</div>
                <button class="floating-close" onclick="hideFloatingComparison()">✕</button>
            </div>
            <div class="floating-selection-info" id="floatingSelectionInfo">
                Aucune sélection
            </div>
            <div class="floating-actions">
                <button class="btn btn-primary" id="floatingCompareBtn" onclick="compareSelectedBuilds()" disabled>
                    Comparer
                </button>
                <button class="btn btn-secondary" onclick="clearAllSelections()">
                    Effacer
                </button>
            </div>
        </div>
    </div>

    <script>
        // Global variables for pagination and selection
        window.currentSuivlines = [];
        window.currentPage = 0;
        window.itemsPerPage = 8;
        window.detailedView = false;
        window.selectedItems = new Set();

        function goBack() {
            window.location.href = 'index_v5.html';
        }

        function getSavedBuilds() {
            const saved = localStorage.getItem('dofus-saved-builds');
            return saved ? JSON.parse(saved) : [];
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            loadSuivlines();
        });

        function loadSuivlines() {
            const savedSuivlines = getSavedBuilds();
            const sortedSuivlines = [...savedSuivlines].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
            
            window.currentSuivlines = sortedSuivlines;
            window.currentPage = 0;
            window.selectedItems = new Set();
            
            refreshBuildList();
            updateResultsCount();
        }

        function updateResultsCount() {
            const resultsCount = document.getElementById('resultsCount');
            resultsCount.textContent = `${window.currentSuivlines.length} Suivlines`;
        }

        function refreshBuildList() {
            const buildList = document.getElementById('buildList');
            const paginationControls = document.getElementById('paginationControls');

            buildList.innerHTML = createBuildListHTML(window.currentSuivlines, window.currentPage * window.itemsPerPage, window.itemsPerPage);
            paginationControls.innerHTML = createPaginationHTML(window.currentSuivlines.length, window.currentPage, window.itemsPerPage);

            // Restore selections from global set
            if (window.selectedItems) {
                window.selectedItems.forEach(value => {
                    const checkbox = document.querySelector(`input[value="${value}"]`);
                    if (checkbox) {
                        checkbox.checked = true;
                    }
                });
            }

            // Update selection state
            updateCompareSelection();
        }

        function createBuildListHTML(suivlines, startIndex, itemsPerPage) {
            const endIndex = Math.min(startIndex + itemsPerPage, suivlines.length);
            let html = '';

            // Get the original saved builds array for proper index mapping
            const allSavedBuilds = getSavedBuilds();

            for (let i = startIndex; i < endIndex; i++) {
                const suivline = suivlines[i];
                // Find the original index in the saved builds array using the unique id
                const originalIndex = allSavedBuilds.findIndex(s => s.id === suivline.id);

                if (window.detailedView) {
                    html += createDetailedBuildItem(suivline, originalIndex);
                } else {
                    html += createCompactBuildItem(suivline, originalIndex);
                }
            }

            return html;
        }

        function createCompactBuildItem(suivline, index) {
            const date = new Date(suivline.timestamp).toLocaleDateString();
            const statsCount = countSuivlineStats(suivline.selectedStats);
            const isRecent = (Date.now() - new Date(suivline.timestamp)) < 24 * 60 * 60 * 1000;

            return `
                <div class="build-item compact clickable" data-index="${index}" onclick="toggleBuildSelection('build-${index}', event)">
                    <div class="build-header">
                        <div class="build-checkbox">
                            <input type="checkbox" value="build-${index}" onchange="updateCompareSelection()">
                        </div>
                        <div class="build-info">
                            <div class="build-name">
                                ${suivline.name}
                                ${isRecent ? '<span class="recent-badge">Nouveau</span>' : ''}
                            </div>
                            <div class="build-meta">
                                <span class="build-date">📅 ${date}</span>
                                <span class="build-stats">⚔️ ${statsCount} stats</span>
                            </div>
                        </div>
                        <div class="build-actions">
                            <button class="btn btn-small" onclick="toggleBuildDetails(${index}, event)">
                                📋 Détails
                            </button>
                            <button class="btn btn-small" onclick="loadSuivline(${index}, event)">
                                📥 Charger
                            </button>
                            <button class="btn btn-small btn-danger" onclick="deleteSuivline(${index}, event)">
                                🗑️
                            </button>
                        </div>
                    </div>
                    <div class="stats-visual-display">
                        ${createVisualStatsDisplay(suivline)}
                    </div>
                </div>
            `;
        }

        function countSuivlineStats(selectedStats) {
            let count = 0;
            selectedStats.forEach((tierStats, tierIndex) => {
                if (tierIndex === 5 || tierIndex === 6) { // S Tier or SS Tier
                    if (Array.isArray(tierStats) && tierStats.length > 0) {
                        count += tierStats.length;
                    }
                } else { // Regular tiers
                    if (Array.isArray(tierStats)) {
                        count += tierStats.filter(stat => stat).length;
                    }
                }
            });
            return count;
        }

        function createVisualStatsDisplay(suivline) {
            let html = '';

            suivline.selectedStats.forEach((tierStats, tierIndex) => {
                const tierStatsExtracted = extractStatsFromTier(tierStats, tierIndex);
                if (tierStatsExtracted.length > 0) {
                    const tierName = suivline.tierNames[tierIndex];
                    html += `
                        <div class="tier-characteristics">
                            <div class="tier-char-header">${tierName}</div>
                            <div class="tier-char-list">
                                ${tierStatsExtracted.map(stat => `<span class="tier-char-item">${stat}</span>`).join('')}
                            </div>
                        </div>
                    `;
                }
            });

            if (html === '') {
                return '<span class="no-preview">Aucune statistique sélectionnée</span>';
            }

            return html;
        }

        function createCompactQuickPreview(suivline) {
            const tierPreviews = [];
            suivline.selectedStats.forEach((tierStats, tierIndex) => {
                const tierStatsExtracted = extractStatsFromTier(tierStats, tierIndex);
                if (tierStatsExtracted.length > 0) {
                    const tierName = suivline.tierNames[tierIndex];
                    tierPreviews.push(`${tierName}(${tierStatsExtracted.length})`);
                }
            });

            if (tierPreviews.length === 0) {
                return '<span class="no-preview">Aucune statistique</span>';
            }

            return `
                <div class="compact-preview-items">
                    ${tierPreviews.join(' • ')}
                </div>
            `;
        }

        function extractStatsFromTier(tierStats, tierIndex) {
            const extracted = [];
            if (tierIndex === 5 || tierIndex === 6) { // S Tier or SS Tier
                if (Array.isArray(tierStats) && tierStats.length > 0) {
                    tierStats.forEach(entry => {
                        if (entry && entry.stat) {
                            extracted.push(entry.stat);
                        }
                    });
                }
            } else { // Regular tiers
                if (Array.isArray(tierStats)) {
                    tierStats.forEach(stat => {
                        if (stat) {
                            extracted.push(stat);
                        }
                    });
                }
            }
            return extracted;
        }

        function createDetailedBuildItem(suivline, index) {
            const date = new Date(suivline.timestamp).toLocaleDateString();
            const statsCount = countSuivlineStats(suivline.selectedStats);
            const isRecent = (Date.now() - new Date(suivline.timestamp)) < 24 * 60 * 60 * 1000;

            return `
                <div class="build-item detailed" data-index="${index}">
                    <div class="build-header-detailed">
                        <div class="build-checkbox">
                            <input type="checkbox" value="build-${index}" onchange="updateCompareSelection()">
                        </div>
                        <div class="build-info-detailed clickable" onclick="toggleBuildSelection('build-${index}', event)">
                            <div class="build-name">
                                ${suivline.name}
                                ${isRecent ? '<span class="recent-badge">Nouveau</span>' : ''}
                            </div>
                            <div class="build-meta">
                                <span class="build-date">📅 ${date}</span>
                                <span class="build-stats">⚔️ ${statsCount} stats</span>
                            </div>
                        </div>
                        <div class="build-actions">
                            <button class="btn btn-small" onclick="toggleBuildDetails(${index}, event)">
                                📋 Compact
                            </button>
                            <button class="btn btn-small" onclick="loadSuivline(${index}, event)">
                                📥 Charger
                            </button>
                            <button class="btn btn-small btn-danger" onclick="deleteSuivline(${index}, event)">
                                🗑️
                            </button>
                        </div>
                    </div>
                    <div class="tier-selection-grid">
                        ${createTierSelectionGrid(suivline, index)}
                    </div>
                </div>
            `;
        }

        function createTierSelectionGrid(suivline, buildIndex) {
            let html = '<div class="tier-grid-header">Sélectionner par tier:</div>';
            html += '<div class="tier-grid">';

            suivline.selectedStats.forEach((tierStats, tierIndex) => {
                const tierStatsExtracted = extractStatsFromTier(tierStats, tierIndex);
                const tierName = suivline.tierNames[tierIndex];
                const hasStats = tierStatsExtracted.length > 0;
                const tierValue = `tier-${buildIndex}-${tierIndex}`;

                html += `
                    <div class="tier-selection-item ${!hasStats ? 'disabled' : 'clickable'}"
                         onclick="${hasStats ? `toggleTierSelection('${tierValue}', event)` : ''}">
                        <div class="tier-checkbox">
                            <input type="checkbox"
                                   value="${tierValue}"
                                   ${!hasStats ? 'disabled' : ''}
                                   onchange="updateCompareSelection()">
                        </div>
                        <div class="tier-info">
                            <div class="tier-name">${tierName}</div>
                            <div class="tier-stats-count">${tierStatsExtracted.length} stats</div>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            return html;
        }

        function createPaginationHTML(totalItems, currentPage, itemsPerPage) {
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            if (totalPages <= 1) return '';

            let html = '<div class="pagination">';

            // Previous button
            const prevDisabled = currentPage === 0;
            html += `<button class="btn btn-small ${prevDisabled ? 'disabled' : ''}"
                     onclick="${prevDisabled ? '' : `changePage(${currentPage - 1})`}">‹ Précédent</button>`;

            // Page numbers (show max 5 pages)
            const startPage = Math.max(0, currentPage - 2);
            const endPage = Math.min(totalPages - 1, startPage + 4);

            for (let i = startPage; i <= endPage; i++) {
                html += `<button class="btn btn-small ${i === currentPage ? 'active' : ''}"
                         onclick="changePage(${i})">${i + 1}</button>`;
            }

            // Next button
            const nextDisabled = currentPage === totalPages - 1;
            html += `<button class="btn btn-small ${nextDisabled ? 'disabled' : ''}"
                     onclick="${nextDisabled ? '' : `changePage(${currentPage + 1})`}">Suivant ›</button>`;

            html += '</div>';
            return html;
        }

        function changePage(newPage) {
            window.currentPage = newPage;
            refreshBuildList();
        }

        function toggleBuildView() {
            window.detailedView = !window.detailedView;
            const toggleBtn = document.getElementById('toggleViewBtn');
            toggleBtn.textContent = window.detailedView ? '📋 Vue compacte' : '📋 Vue détaillée';
            refreshBuildList();
        }

        function filterSuivlines() {
            const searchTerm = document.getElementById('suivlineSearch').value.toLowerCase();
            const allSuivlines = getSavedBuilds().sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

            if (!searchTerm) {
                window.currentSuivlines = allSuivlines;
            } else {
                window.currentSuivlines = allSuivlines.filter(suivline => {
                    return suivline.name.toLowerCase().includes(searchTerm) ||
                           suivline.tierNames.some(tierName => tierName.toLowerCase().includes(searchTerm));
                });
            }

            window.currentPage = 0;
            refreshBuildList();
            updateResultsCount();
        }

        function toggleBuildSelection(value, event) {
            if (event) event.stopPropagation();
            const checkbox = document.querySelector(`input[value="${value}"]`);
            if (checkbox) {
                checkbox.checked = !checkbox.checked;
                updateCompareSelection();
            }
        }

        function toggleTierSelection(value, event) {
            if (event) event.stopPropagation();
            const checkbox = document.querySelector(`input[value="${value}"]`);
            if (checkbox && !checkbox.disabled) {
                checkbox.checked = !checkbox.checked;
                updateCompareSelection();
            }
        }

        function updateCompareSelection() {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
            const compareBtn = document.getElementById('compareSelectedBtn');
            const selectionInfo = document.getElementById('selectionInfo');
            const floatingCompareBtn = document.getElementById('floatingCompareBtn');
            const floatingSelectionInfo = document.getElementById('floatingSelectionInfo');
            const floatingPanel = document.getElementById('floatingComparison');

            const selectedItems = Array.from(checkboxes).map(cb => cb.value);

            // Update global selection set
            if (window.selectedItems) {
                window.selectedItems.clear();
                selectedItems.forEach(item => window.selectedItems.add(item));
            }

            const buildSelections = selectedItems.filter(item => item.startsWith('build-'));
            const tierSelections = selectedItems.filter(item => item.startsWith('tier-'));

            const totalSelections = buildSelections.length + tierSelections.length;

            // Update main comparison section
            compareBtn.disabled = totalSelections < 2;

            if (totalSelections < 2) {
                compareBtn.textContent = 'Comparer les sélectionnés';
                selectionInfo.textContent = 'Sélectionnez au moins 2 éléments pour comparer';
            } else {
                compareBtn.textContent = `Comparer les ${totalSelections} sélectionnés`;

                let infoText = '';
                if (buildSelections.length > 0) {
                    infoText += `${buildSelections.length} Suivline${buildSelections.length > 1 ? 's' : ''}`;
                }
                if (tierSelections.length > 0) {
                    if (infoText) infoText += ' + ';
                    infoText += `${tierSelections.length} tier${tierSelections.length > 1 ? 's' : ''}`;
                }
                selectionInfo.textContent = infoText + ' sélectionnés';
            }

            // Update floating panel
            updateFloatingComparison(totalSelections, buildSelections, tierSelections);
        }

        function updateFloatingComparison(totalSelections, buildSelections, tierSelections) {
            const floatingCompareBtn = document.getElementById('floatingCompareBtn');
            const floatingSelectionInfo = document.getElementById('floatingSelectionInfo');
            const floatingPanel = document.getElementById('floatingComparison');

            if (totalSelections === 0) {
                floatingPanel.classList.remove('visible', 'has-selections');
                floatingSelectionInfo.textContent = 'Aucune sélection';
                floatingCompareBtn.disabled = true;
                floatingCompareBtn.textContent = 'Comparer';
            } else {
                floatingPanel.classList.add('visible');

                if (totalSelections >= 2) {
                    floatingPanel.classList.add('has-selections');
                    floatingCompareBtn.disabled = false;
                    floatingCompareBtn.textContent = `Comparer (${totalSelections})`;
                } else {
                    floatingPanel.classList.remove('has-selections');
                    floatingCompareBtn.disabled = true;
                    floatingCompareBtn.textContent = 'Comparer';
                }

                let infoText = '';
                if (buildSelections.length > 0) {
                    infoText += `${buildSelections.length} Suivline${buildSelections.length > 1 ? 's' : ''}`;
                }
                if (tierSelections.length > 0) {
                    if (infoText) infoText += ' + ';
                    infoText += `${tierSelections.length} tier${tierSelections.length > 1 ? 's' : ''}`;
                }
                floatingSelectionInfo.textContent = infoText + ' sélectionnés';
            }
        }

        function hideFloatingComparison() {
            const floatingPanel = document.getElementById('floatingComparison');
            floatingPanel.classList.remove('visible');
        }

        function clearAllSelections() {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = false);
            window.selectedItems.clear();
            updateCompareSelection();
            hideFloatingComparison();
        }

        function toggleBuildDetails(buildIndex, event) {
            if (event) event.stopPropagation();

            const buildItem = document.querySelector(`[data-index="${buildIndex}"]`);
            if (!buildItem) return;

            // Find the suivline - buildIndex is the global index in the original saved builds array
            const allSuivlines = getSavedBuilds();
            const suivline = allSuivlines[buildIndex];
            if (!suivline) return;

            if (buildItem.classList.contains('compact')) {
                // Switch to detailed view for this item
                buildItem.outerHTML = createDetailedBuildItem(suivline, buildIndex);
            } else {
                // Switch back to compact view
                buildItem.outerHTML = createCompactBuildItem(suivline, buildIndex);
            }

            // Restore selections from global set after DOM update
            if (window.selectedItems) {
                window.selectedItems.forEach(value => {
                    const checkbox = document.querySelector(`input[value="${value}"]`);
                    if (checkbox) {
                        checkbox.checked = true;
                    }
                });
            }

            // Update selection state
            updateCompareSelection();
        }

        function loadSuivline(index, event) {
            if (event) event.stopPropagation();

            const savedSuivlines = getSavedBuilds();
            const suivline = savedSuivlines[index];

            if (!suivline) return;

            // Store the selected stats in localStorage for the main page to load
            localStorage.setItem('loadSuivlineData', JSON.stringify(suivline));

            // Redirect back to main page
            window.location.href = 'index_v5.html?load=true';
        }

        function deleteSuivline(index, event) {
            if (event) event.stopPropagation();

            if (!confirm('Êtes-vous sûr de vouloir supprimer cette Suivline ?')) {
                return;
            }

            const savedSuivlines = getSavedBuilds();
            savedSuivlines.splice(index, 1);
            localStorage.setItem('dofus-saved-builds', JSON.stringify(savedSuivlines));

            // Reload the page
            loadSuivlines();
        }

        function compareSelectedBuilds() {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
            const selectedItems = Array.from(checkboxes).map(cb => cb.value);
            const savedSuivlines = getSavedBuilds();

            const comparisonItems = [];

            selectedItems.forEach(item => {
                if (item.startsWith('build-')) {
                    const buildIndex = parseInt(item.replace('build-', ''));
                    const suivline = savedSuivlines[buildIndex];
                    comparisonItems.push({
                        type: 'build',
                        name: suivline.name,
                        data: suivline,
                        displayName: suivline.name
                    });
                } else if (item.startsWith('tier-')) {
                    const parts = item.replace('tier-', '').split('-');
                    const buildIndex = parseInt(parts[0]);
                    const tierIndex = parseInt(parts[1]);
                    const suivline = savedSuivlines[buildIndex];
                    const tierName = suivline.tierNames[tierIndex];

                    comparisonItems.push({
                        type: 'tier',
                        name: `${suivline.name} - ${tierName}`,
                        data: {
                            ...suivline,
                            selectedStats: [suivline.selectedStats[tierIndex]],
                            tierNames: [tierName]
                        },
                        displayName: `${suivline.name} (${tierName})`
                    });
                }
            });

            // Show comparison results
            showComparisonResults(comparisonItems);
        }

        function showComparisonResults(items) {
            const resultsDiv = document.getElementById('comparisonResults');
            resultsDiv.style.display = 'block';

            let comparisonHTML = '<h3>📊 Résultats de la comparaison</h3>';
            comparisonHTML += '<div class="comparison-grid">';

            items.forEach(item => {
                comparisonHTML += createAdvancedComparisonCard(item);
            });

            comparisonHTML += '</div>';
            comparisonHTML += '<button class="btn btn-secondary" onclick="hideComparisonResults()">Fermer la comparaison</button>';

            resultsDiv.innerHTML = comparisonHTML;

            // Scroll to results
            resultsDiv.scrollIntoView({ behavior: 'smooth' });
        }

        function hideComparisonResults() {
            const resultsDiv = document.getElementById('comparisonResults');
            resultsDiv.style.display = 'none';
        }

        function createAdvancedComparisonCard(item) {
            const date = new Date(item.data.timestamp).toLocaleDateString();
            const statsCount = countSuivlineStats(item.data.selectedStats);

            let characteristicsHTML = '';

            item.data.selectedStats.forEach((tierStats, tierIndex) => {
                const tierStatsExtracted = extractStatsFromTier(tierStats, tierIndex);
                if (tierStatsExtracted.length > 0) {
                    const tierName = item.data.tierNames[tierIndex];
                    characteristicsHTML += `
                        <div class="tier-characteristics">
                            <div class="tier-char-header">${tierName}</div>
                            <div class="tier-char-list">
                                ${tierStatsExtracted.map(stat => `<span class="tier-char-item">${stat}</span>`).join('')}
                            </div>
                        </div>
                    `;
                }
            });

            const typeIcon = item.type === 'build' ? '⚔️' : '🎯';
            const typeLabel = item.type === 'build' ? 'Suivline complète' : `Tier: ${item.tierName || 'Spécifique'}`;

            return `
                <div class="comparison-card ${item.type}">
                    <div class="comparison-card-header">
                        <div class="comparison-card-icon">${typeIcon}</div>
                        <div class="comparison-card-info">
                            <div class="comparison-card-name">${item.displayName}</div>
                            <div class="comparison-card-type">${typeLabel}</div>
                            <div class="comparison-card-meta">
                                <span>📅 ${date}</span>
                                <span>⚔️ ${statsCount} stats</span>
                            </div>
                        </div>
                    </div>
                    <div class="comparison-content">
                        ${characteristicsHTML}
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>
