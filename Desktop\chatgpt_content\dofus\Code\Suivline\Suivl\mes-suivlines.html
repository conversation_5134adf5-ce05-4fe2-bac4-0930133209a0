<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mes Suivlines - Dofus Stats</title>
    <link rel="stylesheet" href="style.css">
</head>
<body data-theme="light">
    <!-- Global Header -->
    <header class="global-header">
        <div class="header-container">
            <a href="index_v5.html" class="header-logo">
                ⚔️ Dofus Stats
            </a>

            <nav class="header-nav">
                <a href="index_v5.html" class="header-nav-item">🏠 Suivlines Builder</a>
                <a href="mes-suivlines.html" class="header-nav-item active">📁 Mes Suivlines</a>
            </nav>

            <div class="header-controls">
                <!-- Language Selector -->
                <div class="header-dropdown">
                    <button class="header-dropdown-btn" id="languageBtn">
                        🌐 <span id="currentLanguage">FR</span> ▼
                    </button>
                    <div class="header-dropdown-content">
                        <div class="header-dropdown-item" onclick="changeLanguage('fr')">🇫🇷 Français</div>
                        <div class="header-dropdown-item" onclick="changeLanguage('en')">🇬🇧 English</div>
                    </div>
                </div>

                <!-- Dark Mode Toggle -->
                <button id="darkModeToggle" class="header-dropdown-btn">🌙 Mode sombre</button>
            </div>
        </div>
    </header>

    <div class="main-container">
        <div class="mes-suivlines-container">
            <div class="mes-suivlines-header">
                <h1>📁 Mes Suivlines</h1>
                <p>Gérez et comparez vos builds sauvegardées</p>
            </div>

            <div class="mes-suivlines-comparison-section">
            <h3>🔍 Comparaison de builds</h3>
            <div class="comparison-controls">
                <button class="btn" id="compareSelectedBtn" onclick="compareSelectedBuilds()" disabled>
                    Comparer les sélectionnés
                </button>
                <button class="btn btn-secondary" id="toggleViewBtn" onclick="toggleBuildView()">
                    📋 Vue détaillée
                </button>
                <button class="btn btn-secondary" onclick="clearAllSelections()">
                    🗑️ Effacer sélections
                </button>
            </div>
            <div class="selection-info" id="selectionInfo">
                Sélectionnez au moins 2 éléments pour comparer
            </div>
        </div>

        <div class="search-section">
            <input type="text" id="suivlineSearch" placeholder="🔍 Rechercher une Suivline..." onkeyup="filterSuivlines()">
            <div class="results-count" id="resultsCount">0 Suivlines</div>
        </div>

        <div class="builds-container">
            <div class="builds-grid" id="buildList">
                <!-- Builds will be loaded here -->
            </div>
        </div>

        <div class="pagination-section">
            <div class="pagination-controls" id="paginationControls">
                <!-- Pagination will be loaded here -->
            </div>
        </div>

        <div class="comparison-results" id="comparisonResults" style="display: none;">
            <!-- Comparison results will be shown here -->
        </div>

        <!-- Floating comparison panel -->
        <div class="floating-comparison" id="floatingComparison">
            <div class="floating-header">
                <div class="floating-title">🔍 Comparaison</div>
                <button class="floating-close" onclick="hideFloatingComparison()">✕</button>
            </div>
            <div class="floating-selection-info" id="floatingSelectionInfo">
                Aucune sélection
            </div>
            <div class="floating-actions">
                <button class="btn btn-primary" id="floatingCompareBtn" onclick="compareSelectedBuilds()" disabled>
                    Comparer
                </button>
                <button class="btn btn-secondary" onclick="clearAllSelections()">
                    Effacer
                </button>
            </div>
        </div>
    </div>

    <script>
        // Global variables for pagination and selection
        window.currentSuivlines = [];
        window.currentPage = 0;
        window.itemsPerPage = 16; // 4x4 grid layout
        window.detailedView = false;
        window.selectedItems = new Set();

        // Language functionality
        const translations = {
            fr: {
                title: "Mes Suivlines",
                subtitle: "Gérez et comparez vos builds sauvegardées",
                comparison: "Comparaison de builds",
                compareSelected: "Comparer les sélectionnés",
                detailedView: "Vue détaillée",
                compactView: "Vue compacte",
                clearSelections: "Effacer sélections",
                selectAtLeast: "Sélectionnez au moins 2 éléments pour comparer",
                searchPlaceholder: "🔍 Rechercher une Suivline...",
                suivlines: "Suivlines",
                details: "Détails",
                load: "Charger",
                delete: "Supprimer",
                compare: "Comparer",
                clear: "Effacer",
                noSelection: "Aucune sélection",
                closeComparison: "Fermer la comparaison",
                comparisonResults: "Résultats de la comparaison",
                darkMode: "Mode sombre",
                lightMode: "Mode clair"
            },
            en: {
                title: "My Suivlines",
                subtitle: "Manage and compare your saved builds",
                comparison: "Build comparison",
                compareSelected: "Compare selected",
                detailedView: "Detailed view",
                compactView: "Compact view",
                clearSelections: "Clear selections",
                selectAtLeast: "Select at least 2 items to compare",
                searchPlaceholder: "🔍 Search a Suivline...",
                suivlines: "Suivlines",
                details: "Details",
                load: "Load",
                delete: "Delete",
                compare: "Compare",
                clear: "Clear",
                noSelection: "No selection",
                closeComparison: "Close comparison",
                comparisonResults: "Comparison results",
                darkMode: "Dark mode",
                lightMode: "Light mode"
            }
        };

        let currentLanguage = 'fr';

        function changeLanguage(lang) {
            currentLanguage = lang;
            localStorage.setItem('language', lang);
            updateLanguageDisplay();
            updatePageTexts();
        }

        function updateLanguageDisplay() {
            const currentLangElement = document.getElementById('currentLanguage');
            currentLangElement.textContent = currentLanguage.toUpperCase();

            // Update active state in dropdown
            document.querySelectorAll('.header-dropdown-item').forEach(item => {
                item.classList.remove('active');
                if (item.textContent.includes(currentLanguage === 'fr' ? 'Français' : 'English')) {
                    item.classList.add('active');
                }
            });
        }

        function updatePageTexts() {
            const t = translations[currentLanguage];

            // Update main texts
            document.querySelector('.mes-suivlines-header h1').textContent = `📁 ${t.title}`;
            document.querySelector('.mes-suivlines-header p').textContent = t.subtitle;
            document.querySelector('.mes-suivlines-comparison-section h3').textContent = `🔍 ${t.comparison}`;

            // Update buttons
            document.getElementById('compareSelectedBtn').textContent = t.compareSelected;
            document.getElementById('toggleViewBtn').textContent = window.detailedView ? `📋 ${t.compactView}` : `📋 ${t.detailedView}`;
            document.querySelector('button[onclick="clearAllSelections()"]').textContent = `🗑️ ${t.clearSelections}`;

            // Update selection info
            document.getElementById('selectionInfo').textContent = t.selectAtLeast;
            document.getElementById('suivlineSearch').placeholder = t.searchPlaceholder;

            // Update floating panel
            document.getElementById('floatingSelectionInfo').textContent = t.noSelection;
            document.getElementById('floatingCompareBtn').textContent = t.compare;

            // Refresh build list to update button texts
            refreshBuildList();
        }

        function getSavedBuilds() {
            const saved = localStorage.getItem('dofus-saved-builds');
            return saved ? JSON.parse(saved) : [];
        }

        // Global dark mode functionality
        function initializeDarkMode() {
            const darkModeToggle = document.getElementById('darkModeToggle');
            const body = document.body;

            // Use global theme key shared across all pages
            const savedTheme = localStorage.getItem('dofus-global-theme') || 'light';
            body.setAttribute('data-theme', savedTheme);

            // Update toggle button text
            updateDarkModeToggle(savedTheme);

            // Add click event listener
            darkModeToggle.addEventListener('click', function() {
                const currentTheme = body.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

                body.setAttribute('data-theme', newTheme);
                localStorage.setItem('dofus-global-theme', newTheme);
                updateDarkModeToggle(newTheme);
            });
        }

        function updateDarkModeToggle(theme) {
            const darkModeToggle = document.getElementById('darkModeToggle');
            const t = translations[currentLanguage];
            if (theme === 'dark') {
                darkModeToggle.textContent = `☀️ ${t.lightMode}`;
            } else {
                darkModeToggle.textContent = `🌙 ${t.darkMode}`;
            }
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize language
            currentLanguage = localStorage.getItem('language') || 'fr';
            updateLanguageDisplay();
            updatePageTexts();

            // Initialize dark mode
            initializeDarkMode();

            // Load suivlines
            loadSuivlines();
        });

        function loadSuivlines() {
            const savedSuivlines = getSavedBuilds();
            const sortedSuivlines = [...savedSuivlines].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
            
            window.currentSuivlines = sortedSuivlines;
            window.currentPage = 0;
            window.selectedItems = new Set();
            
            refreshBuildList();
            updateResultsCount();
        }

        function updateResultsCount() {
            const resultsCount = document.getElementById('resultsCount');
            resultsCount.textContent = `${window.currentSuivlines.length} Suivlines`;
        }

        function refreshBuildList() {
            const buildList = document.getElementById('buildList');
            const paginationControls = document.getElementById('paginationControls');

            // Create grid layout HTML
            buildList.innerHTML = createBuildListHTML(window.currentSuivlines, window.currentPage * window.itemsPerPage, window.itemsPerPage);
            paginationControls.innerHTML = createPaginationHTML(window.currentSuivlines.length, window.currentPage, window.itemsPerPage);

            // Restore selections from global set
            if (window.selectedItems) {
                window.selectedItems.forEach(value => {
                    const checkbox = document.querySelector(`input[value="${value}"]`);
                    if (checkbox) {
                        checkbox.checked = true;
                    }
                });
            }

            // Update selection state
            updateCompareSelection();
        }

        function createBuildListHTML(suivlines, startIndex, itemsPerPage) {
            const endIndex = Math.min(startIndex + itemsPerPage, suivlines.length);
            let html = '';

            // Get the original saved builds array for proper index mapping
            const allSavedBuilds = getSavedBuilds();

            for (let i = startIndex; i < endIndex; i++) {
                const suivline = suivlines[i];
                // Find the original index in the saved builds array using the unique id
                const originalIndex = allSavedBuilds.findIndex(s => s.id === suivline.id);

                if (window.detailedView) {
                    html += createDetailedBuildItem(suivline, originalIndex);
                } else {
                    html += createCompactBuildItem(suivline, originalIndex);
                }
            }

            return html;
        }

        function createCompactBuildItem(suivline, index) {
            const date = new Date(suivline.timestamp).toLocaleDateString();
            const statsCount = countSuivlineStats(suivline.selectedStats);
            const isRecent = (Date.now() - new Date(suivline.timestamp)) < 24 * 60 * 60 * 1000;
            const t = translations[currentLanguage];

            return `
                <div class="build-item compact clickable" data-index="${index}" onclick="toggleBuildSelection('build-${index}', event)">
                    <div class="build-header">
                        <div class="build-checkbox">
                            <input type="checkbox" value="build-${index}" onchange="updateCompareSelection()">
                        </div>
                        <div class="build-info">
                            <div class="build-name">
                                ${suivline.name}
                                ${isRecent ? '<span class="recent-badge">' + (currentLanguage === 'fr' ? 'Nouveau' : 'New') + '</span>' : ''}
                            </div>
                            <div class="build-meta">
                                <span class="build-date">📅 ${date}</span>
                                <span class="build-stats">⚔️ ${statsCount} stats</span>
                            </div>
                        </div>
                        <div class="build-actions">
                            <button class="btn btn-small" onclick="toggleBuildDetails(${index}, event)">
                                📋 ${t.details}
                            </button>
                            <button class="btn btn-small" onclick="loadSuivline(${index}, event)">
                                📥 ${t.load}
                            </button>
                            <button class="btn btn-small btn-danger" onclick="deleteSuivline(${index}, event)">
                                🗑️
                            </button>
                        </div>
                    </div>
                    <div class="stats-visual-display">
                        ${createVisualStatsDisplay(suivline)}
                    </div>
                </div>
            `;
        }

        function countSuivlineStats(selectedStats) {
            let count = 0;
            selectedStats.forEach((tierStats, tierIndex) => {
                if (tierIndex === 5 || tierIndex === 6) { // S Tier or SS Tier
                    if (Array.isArray(tierStats) && tierStats.length > 0) {
                        count += tierStats.length;
                    }
                } else { // Regular tiers
                    if (Array.isArray(tierStats)) {
                        count += tierStats.filter(stat => stat).length;
                    }
                }
            });
            return count;
        }

        function createVisualStatsDisplay(suivline) {
            let html = '';

            suivline.selectedStats.forEach((tierStats, tierIndex) => {
                const tierStatsExtracted = extractStatsFromTier(tierStats, tierIndex);
                if (tierStatsExtracted.length > 0) {
                    const tierName = suivline.tierNames[tierIndex];
                    html += `
                        <div class="tier-characteristics">
                            <div class="tier-char-header">${tierName}</div>
                            <div class="tier-char-list">
                                ${tierStatsExtracted.map(stat => `<span class="tier-char-item">${stat}</span>`).join('')}
                            </div>
                        </div>
                    `;
                }
            });

            if (html === '') {
                return '<span class="no-preview">Aucune statistique sélectionnée</span>';
            }

            return html;
        }

        function createCompactQuickPreview(suivline) {
            const tierPreviews = [];
            suivline.selectedStats.forEach((tierStats, tierIndex) => {
                const tierStatsExtracted = extractStatsFromTier(tierStats, tierIndex);
                if (tierStatsExtracted.length > 0) {
                    const tierName = suivline.tierNames[tierIndex];
                    tierPreviews.push(`${tierName}(${tierStatsExtracted.length})`);
                }
            });

            if (tierPreviews.length === 0) {
                return '<span class="no-preview">Aucune statistique</span>';
            }

            return `
                <div class="compact-preview-items">
                    ${tierPreviews.join(' • ')}
                </div>
            `;
        }

        function extractStatsFromTier(tierStats, tierIndex) {
            const extracted = [];
            if (tierIndex === 5 || tierIndex === 6) { // S Tier or SS Tier
                if (Array.isArray(tierStats) && tierStats.length > 0) {
                    tierStats.forEach(entry => {
                        if (entry && entry.stat) {
                            extracted.push(entry.stat);
                        }
                    });
                }
            } else { // Regular tiers
                if (Array.isArray(tierStats)) {
                    tierStats.forEach(stat => {
                        if (stat) {
                            extracted.push(stat);
                        }
                    });
                }
            }
            return extracted;
        }

        function createDetailedBuildItem(suivline, index) {
            const date = new Date(suivline.timestamp).toLocaleDateString();
            const statsCount = countSuivlineStats(suivline.selectedStats);
            const isRecent = (Date.now() - new Date(suivline.timestamp)) < 24 * 60 * 60 * 1000;

            return `
                <div class="build-item detailed" data-index="${index}">
                    <div class="build-header-detailed">
                        <div class="build-checkbox">
                            <input type="checkbox" value="build-${index}" onchange="updateCompareSelection()">
                        </div>
                        <div class="build-info-detailed clickable" onclick="toggleBuildSelection('build-${index}', event)">
                            <div class="build-name">
                                ${suivline.name}
                                ${isRecent ? '<span class="recent-badge">Nouveau</span>' : ''}
                            </div>
                            <div class="build-meta">
                                <span class="build-date">📅 ${date}</span>
                                <span class="build-stats">⚔️ ${statsCount} stats</span>
                            </div>
                        </div>
                        <div class="build-actions">
                            <button class="btn btn-small" onclick="toggleBuildDetails(${index}, event)">
                                📋 Compact
                            </button>
                            <button class="btn btn-small" onclick="loadSuivline(${index}, event)">
                                📥 Charger
                            </button>
                            <button class="btn btn-small btn-danger" onclick="deleteSuivline(${index}, event)">
                                🗑️
                            </button>
                        </div>
                    </div>
                    <div class="tier-selection-grid">
                        ${createTierSelectionGrid(suivline, index)}
                    </div>
                </div>
            `;
        }

        function createTierSelectionGrid(suivline, buildIndex) {
            let html = '<div class="tier-grid-header">Sélectionner par tier:</div>';
            html += '<div class="tier-grid">';

            suivline.selectedStats.forEach((tierStats, tierIndex) => {
                const tierStatsExtracted = extractStatsFromTier(tierStats, tierIndex);
                const tierName = suivline.tierNames[tierIndex];
                const hasStats = tierStatsExtracted.length > 0;
                const tierValue = `tier-${buildIndex}-${tierIndex}`;

                html += `
                    <div class="tier-selection-item ${!hasStats ? 'disabled' : 'clickable'}"
                         onclick="${hasStats ? `toggleTierSelection('${tierValue}', event)` : ''}">
                        <div class="tier-checkbox">
                            <input type="checkbox"
                                   value="${tierValue}"
                                   ${!hasStats ? 'disabled' : ''}
                                   onchange="updateCompareSelection()">
                        </div>
                        <div class="tier-info">
                            <div class="tier-name">${tierName}</div>
                            <div class="tier-stats-count">${tierStatsExtracted.length} stats</div>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            return html;
        }

        function createPaginationHTML(totalItems, currentPage, itemsPerPage) {
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            if (totalPages <= 1) return '';

            let html = '<div class="pagination">';

            // Previous button
            const prevDisabled = currentPage === 0;
            html += `<button class="btn btn-small ${prevDisabled ? 'disabled' : ''}"
                     onclick="${prevDisabled ? '' : `changePage(${currentPage - 1})`}">‹ Précédent</button>`;

            // Page numbers (show max 5 pages)
            const startPage = Math.max(0, currentPage - 2);
            const endPage = Math.min(totalPages - 1, startPage + 4);

            for (let i = startPage; i <= endPage; i++) {
                html += `<button class="btn btn-small ${i === currentPage ? 'active' : ''}"
                         onclick="changePage(${i})">${i + 1}</button>`;
            }

            // Next button
            const nextDisabled = currentPage === totalPages - 1;
            html += `<button class="btn btn-small ${nextDisabled ? 'disabled' : ''}"
                     onclick="${nextDisabled ? '' : `changePage(${currentPage + 1})`}">Suivant ›</button>`;

            html += '</div>';
            return html;
        }

        function changePage(newPage) {
            window.currentPage = newPage;
            refreshBuildList();
        }

        function toggleBuildView() {
            window.detailedView = !window.detailedView;
            const toggleBtn = document.getElementById('toggleViewBtn');
            toggleBtn.textContent = window.detailedView ? '📋 Vue compacte' : '📋 Vue détaillée';
            refreshBuildList();
        }

        function filterSuivlines() {
            const searchTerm = document.getElementById('suivlineSearch').value.toLowerCase();
            const allSuivlines = getSavedBuilds().sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

            if (!searchTerm) {
                window.currentSuivlines = allSuivlines;
            } else {
                window.currentSuivlines = allSuivlines.filter(suivline => {
                    return suivline.name.toLowerCase().includes(searchTerm) ||
                           suivline.tierNames.some(tierName => tierName.toLowerCase().includes(searchTerm));
                });
            }

            window.currentPage = 0;
            refreshBuildList();
            updateResultsCount();
        }

        function toggleBuildSelection(value, event) {
            if (event) event.stopPropagation();
            const checkbox = document.querySelector(`input[value="${value}"]`);
            if (checkbox) {
                checkbox.checked = !checkbox.checked;
                updateCompareSelection();
            }
        }

        function toggleTierSelection(value, event) {
            if (event) event.stopPropagation();
            const checkbox = document.querySelector(`input[value="${value}"]`);
            if (checkbox && !checkbox.disabled) {
                checkbox.checked = !checkbox.checked;
                updateCompareSelection();
            }
        }

        function updateCompareSelection() {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
            const compareBtn = document.getElementById('compareSelectedBtn');
            const selectionInfo = document.getElementById('selectionInfo');
            const floatingCompareBtn = document.getElementById('floatingCompareBtn');
            const floatingSelectionInfo = document.getElementById('floatingSelectionInfo');
            const floatingPanel = document.getElementById('floatingComparison');

            const selectedItems = Array.from(checkboxes).map(cb => cb.value);

            // Update global selection set
            if (window.selectedItems) {
                window.selectedItems.clear();
                selectedItems.forEach(item => window.selectedItems.add(item));
            }

            const buildSelections = selectedItems.filter(item => item.startsWith('build-'));
            const tierSelections = selectedItems.filter(item => item.startsWith('tier-'));

            const totalSelections = buildSelections.length + tierSelections.length;

            // Update main comparison section
            compareBtn.disabled = totalSelections < 2;

            if (totalSelections < 2) {
                compareBtn.textContent = 'Comparer les sélectionnés';
                selectionInfo.textContent = 'Sélectionnez au moins 2 éléments pour comparer';
            } else {
                compareBtn.textContent = `Comparer les ${totalSelections} sélectionnés`;

                let infoText = '';
                if (buildSelections.length > 0) {
                    infoText += `${buildSelections.length} Suivline${buildSelections.length > 1 ? 's' : ''}`;
                }
                if (tierSelections.length > 0) {
                    if (infoText) infoText += ' + ';
                    infoText += `${tierSelections.length} tier${tierSelections.length > 1 ? 's' : ''}`;
                }
                selectionInfo.textContent = infoText + ' sélectionnés';
            }

            // Update floating panel
            updateFloatingComparison(totalSelections, buildSelections, tierSelections);
        }

        function updateFloatingComparison(totalSelections, buildSelections, tierSelections) {
            const floatingCompareBtn = document.getElementById('floatingCompareBtn');
            const floatingSelectionInfo = document.getElementById('floatingSelectionInfo');
            const floatingPanel = document.getElementById('floatingComparison');

            if (totalSelections === 0) {
                floatingPanel.classList.remove('visible', 'has-selections');
                floatingSelectionInfo.textContent = 'Aucune sélection';
                floatingCompareBtn.disabled = true;
                floatingCompareBtn.textContent = 'Comparer';
            } else {
                floatingPanel.classList.add('visible');

                if (totalSelections >= 2) {
                    floatingPanel.classList.add('has-selections');
                    floatingCompareBtn.disabled = false;
                    floatingCompareBtn.textContent = `Comparer (${totalSelections})`;
                } else {
                    floatingPanel.classList.remove('has-selections');
                    floatingCompareBtn.disabled = true;
                    floatingCompareBtn.textContent = 'Comparer';
                }

                let infoText = '';
                if (buildSelections.length > 0) {
                    infoText += `${buildSelections.length} Suivline${buildSelections.length > 1 ? 's' : ''}`;
                }
                if (tierSelections.length > 0) {
                    if (infoText) infoText += ' + ';
                    infoText += `${tierSelections.length} tier${tierSelections.length > 1 ? 's' : ''}`;
                }
                floatingSelectionInfo.textContent = infoText + ' sélectionnés';
            }
        }

        function hideFloatingComparison() {
            const floatingPanel = document.getElementById('floatingComparison');
            floatingPanel.classList.remove('visible');
        }

        function clearAllSelections() {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = false);
            window.selectedItems.clear();
            updateCompareSelection();
            hideFloatingComparison();
        }

        function toggleBuildDetails(buildIndex, event) {
            if (event) event.stopPropagation();

            const buildItem = document.querySelector(`[data-index="${buildIndex}"]`);
            if (!buildItem) return;

            // Find the suivline - buildIndex is the global index in the original saved builds array
            const allSuivlines = getSavedBuilds();
            const suivline = allSuivlines[buildIndex];
            if (!suivline) return;

            if (buildItem.classList.contains('compact')) {
                // Switch to detailed view for this item
                buildItem.outerHTML = createDetailedBuildItem(suivline, buildIndex);
            } else {
                // Switch back to compact view
                buildItem.outerHTML = createCompactBuildItem(suivline, buildIndex);
            }

            // Restore selections from global set after DOM update
            if (window.selectedItems) {
                window.selectedItems.forEach(value => {
                    const checkbox = document.querySelector(`input[value="${value}"]`);
                    if (checkbox) {
                        checkbox.checked = true;
                    }
                });
            }

            // Update selection state
            updateCompareSelection();
        }

        function loadSuivline(index, event) {
            if (event) event.stopPropagation();

            const savedSuivlines = getSavedBuilds();
            const suivline = savedSuivlines[index];

            if (!suivline) return;

            // Store the selected stats in localStorage for the main page to load
            localStorage.setItem('loadSuivlineData', JSON.stringify(suivline));

            // Redirect back to main page
            window.location.href = 'index_v5.html?load=true';
        }

        function deleteSuivline(index, event) {
            if (event) event.stopPropagation();

            if (!confirm('Êtes-vous sûr de vouloir supprimer cette Suivline ?')) {
                return;
            }

            const savedSuivlines = getSavedBuilds();
            savedSuivlines.splice(index, 1);
            localStorage.setItem('dofus-saved-builds', JSON.stringify(savedSuivlines));

            // Reload the page
            loadSuivlines();
        }

        function compareSelectedBuilds() {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
            const selectedItems = Array.from(checkboxes).map(cb => cb.value);
            const savedSuivlines = getSavedBuilds();

            const comparisonItems = [];

            selectedItems.forEach(item => {
                if (item.startsWith('build-')) {
                    const buildIndex = parseInt(item.replace('build-', ''));
                    const suivline = savedSuivlines[buildIndex];
                    comparisonItems.push({
                        type: 'build',
                        name: suivline.name,
                        data: suivline,
                        displayName: suivline.name
                    });
                } else if (item.startsWith('tier-')) {
                    const parts = item.replace('tier-', '').split('-');
                    const buildIndex = parseInt(parts[0]);
                    const tierIndex = parseInt(parts[1]);
                    const suivline = savedSuivlines[buildIndex];
                    const tierName = suivline.tierNames[tierIndex];

                    comparisonItems.push({
                        type: 'tier',
                        name: `${suivline.name} - ${tierName}`,
                        data: {
                            ...suivline,
                            selectedStats: [suivline.selectedStats[tierIndex]],
                            tierNames: [tierName]
                        },
                        displayName: `${suivline.name} (${tierName})`
                    });
                }
            });

            // Show comparison results
            showComparisonResults(comparisonItems);
        }

        function showComparisonResults(items) {
            const resultsDiv = document.getElementById('comparisonResults');
            resultsDiv.style.display = 'block';

            let comparisonHTML = '<h3>📊 Résultats de la comparaison</h3>';
            comparisonHTML += '<div class="comparison-grid">';

            items.forEach(item => {
                comparisonHTML += createAdvancedComparisonCard(item);
            });

            comparisonHTML += '</div>';
            comparisonHTML += '<button class="btn btn-secondary" onclick="hideComparisonResults()">Fermer la comparaison</button>';

            resultsDiv.innerHTML = comparisonHTML;

            // Scroll to results
            resultsDiv.scrollIntoView({ behavior: 'smooth' });
        }

        function hideComparisonResults() {
            const resultsDiv = document.getElementById('comparisonResults');
            resultsDiv.style.display = 'none';
        }

        function createAdvancedComparisonCard(item) {
            const date = new Date(item.data.timestamp).toLocaleDateString();
            const statsCount = countSuivlineStats(item.data.selectedStats);

            let characteristicsHTML = '';

            item.data.selectedStats.forEach((tierStats, tierIndex) => {
                const tierStatsExtracted = extractStatsFromTier(tierStats, tierIndex);
                if (tierStatsExtracted.length > 0) {
                    const tierName = item.data.tierNames[tierIndex];
                    characteristicsHTML += `
                        <div class="tier-characteristics">
                            <div class="tier-char-header">${tierName}</div>
                            <div class="tier-char-list">
                                ${tierStatsExtracted.map(stat => `<span class="tier-char-item">${stat}</span>`).join('')}
                            </div>
                        </div>
                    `;
                }
            });

            const typeIcon = item.type === 'build' ? '⚔️' : '🎯';
            const typeLabel = item.type === 'build' ? 'Suivline complète' : `Tier: ${item.tierName || 'Spécifique'}`;

            return `
                <div class="comparison-card ${item.type}">
                    <div class="comparison-card-header">
                        <div class="comparison-card-icon">${typeIcon}</div>
                        <div class="comparison-card-info">
                            <div class="comparison-card-name">${item.displayName}</div>
                            <div class="comparison-card-type">${typeLabel}</div>
                            <div class="comparison-card-meta">
                                <span>📅 ${date}</span>
                                <span>⚔️ ${statsCount} stats</span>
                            </div>
                        </div>
                    </div>
                    <div class="comparison-content">
                        ${characteristicsHTML}
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>
